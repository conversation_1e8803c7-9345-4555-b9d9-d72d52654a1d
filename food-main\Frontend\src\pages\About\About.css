/* About Page Styles */
.about-page {
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed navbar */
}

/* About Hero Section */
.about-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
}

.about-hero-content h1 {
  font-size: max(3.5vw, 42px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.about-hero-content p {
  font-size: max(1.2vw, 20px);
  margin: 0;
  opacity: 0.9;
}

/* Our Story Section */
.our-story {
  padding: 100px 0;
  background: white;
}

.story-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.story-text h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 30px 0;
}

.story-text p {
  font-size: 18px;
  line-height: 1.8;
  color: #7f8c8d;
  margin: 0 0 25px 0;
}

.story-stats {
  display: flex;
  gap: 30px;
  margin-top: 40px;
}

.story-stat {
  text-align: center;
}

.story-stat .stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 5px;
}

.story-stat .stat-label {
  font-size: 14px;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.story-image img {
  width: 100%;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Our Values Section */
.our-values {
  padding: 100px 0;
  background: #f8f9fa;
}

.values-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.values-container h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 60px 0;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.value-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.value-card h3 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.value-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Team Section */
.our-team {
  padding: 100px 0;
  background: white;
}

.team-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.team-container h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 60px 0;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.team-member {
  background: #f8f9fa;
  padding: 40px 30px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.member-image {
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid #667eea;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-member h3 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.member-role {
  color: #667eea;
  font-weight: 600;
  margin: 0 0 15px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
}

.member-bio {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Mission Section */
.our-mission {
  padding: 100px 0;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
}

.mission-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.mission-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.mission-content h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  margin: 0 0 30px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.mission-content p {
  font-size: 18px;
  line-height: 1.8;
  opacity: 0.9;
  margin: 0 0 40px 0;
}

.mission-points {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mission-point {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 16px;
  font-weight: 500;
}

.point-icon {
  font-size: 24px;
}

.mission-image img {
  width: 100%;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Call to Action */
.about-cta {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.cta-container h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.cta-container p {
  font-size: 20px;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.cta-btn {
  padding: 15px 40px;
  border: none;
  border-radius: 30px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-btn.primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.cta-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

.cta-btn.secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.cta-btn.secondary:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .story-content,
  .mission-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .story-stats {
    justify-content: center;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .about-page {
    padding-top: 60px;
  }

  .about-hero {
    padding: 80px 0;
  }

  .about-hero-content h1 {
    font-size: 32px;
  }

  .about-hero-content p {
    font-size: 18px;
  }

  .our-story,
  .our-values,
  .our-team,
  .our-mission,
  .about-cta {
    padding: 80px 0;
  }

  .story-stats {
    flex-direction: column;
    gap: 20px;
  }

  .values-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .story-container,
  .values-container,
  .team-container,
  .mission-container,
  .cta-container {
    padding: 0 15px;
  }

  .about-hero-content h1 {
    font-size: 28px;
  }

  .story-text h2,
  .values-container h2,
  .team-container h2,
  .mission-content h2,
  .cta-container h2 {
    font-size: 28px;
  }

  .value-card,
  .team-member {
    padding: 30px 20px;
  }
}
