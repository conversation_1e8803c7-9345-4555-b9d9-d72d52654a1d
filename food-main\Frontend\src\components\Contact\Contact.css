/* Contact Section */
.contact-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.contact-header {
  text-align: center;
  margin-bottom: 60px;
}

.contact-header h2 {
  font-size: max(3vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.contact-header p {
  font-size: max(1.2vw, 18px);
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Contact Content */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 60px;
  margin-bottom: 60px;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.info-card {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.info-icon {
  font-size: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.info-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.info-content p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

.info-content a {
  color: #667eea;
  text-decoration: none;
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Social Media */
.social-media {
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.social-media h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  text-decoration: none;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.social-link:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Contact Form */
.contact-form-container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h3 {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.form-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 15px;
  border: 2px solid #e0e6ed;
  border-radius: 10px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 18px 40px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-btn.submitting {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-icon {
  font-size: 18px;
}

/* Success Message */
.success-message {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.success-icon {
  font-size: 20px;
}

/* Map Section */
.map-section {
  margin-bottom: 60px;
  text-align: center;
}

.map-section h3 {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 30px 0;
}

.map-container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
}

.map-placeholder {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 15px;
  padding: 60px 20px;
  text-align: center;
  color: #6c757d;
}

.map-icon {
  font-size: 60px;
  margin-bottom: 15px;
}

.map-placeholder p {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.map-placeholder small {
  font-size: 14px;
  opacity: 0.8;
}

/* Quick Contact */
.quick-contact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.quick-contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.quick-contact-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.quick-icon {
  font-size: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.quick-contact-item h4 {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.quick-contact-item p {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-form-container {
    padding: 30px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .quick-contact {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .contact-section {
    padding: 60px 0;
  }

  .contact-container {
    padding: 0 15px;
  }

  .contact-header h2 {
    font-size: 28px;
  }

  .contact-header p {
    font-size: 16px;
  }

  .contact-content {
    gap: 30px;
    margin-bottom: 40px;
  }

  .info-card {
    padding: 20px;
    gap: 15px;
  }

  .info-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .info-content h3 {
    font-size: 18px;
  }

  .contact-form-container {
    padding: 25px;
  }

  .form-header h3 {
    font-size: 24px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 12px;
    font-size: 14px;
  }

  .submit-btn {
    padding: 15px 30px;
    font-size: 14px;
  }

  .map-container {
    padding: 30px;
  }

  .map-placeholder {
    padding: 40px 20px;
  }

  .map-icon {
    font-size: 40px;
  }

  .quick-contact {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .quick-contact-item {
    padding: 20px;
    gap: 15px;
  }

  .quick-icon {
    width: 60px;
    height: 60px;
    font-size: 30px;
  }

  .social-links {
    gap: 10px;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .contact-section {
    padding: 40px 0;
  }

  .contact-container {
    padding: 0 10px;
  }

  .contact-header {
    margin-bottom: 40px;
  }

  .contact-header h2 {
    font-size: 24px;
  }

  .contact-header p {
    font-size: 14px;
  }

  .info-card {
    padding: 15px;
    gap: 12px;
  }

  .info-icon {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .info-content h3 {
    font-size: 16px;
  }

  .info-content p {
    font-size: 14px;
  }

  .contact-form-container {
    padding: 20px;
  }

  .form-header h3 {
    font-size: 20px;
  }

  .form-header p {
    font-size: 14px;
  }

  .form-group label {
    font-size: 12px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 10px;
    font-size: 14px;
  }

  .submit-btn {
    padding: 12px 24px;
    font-size: 13px;
  }

  .map-section h3 {
    font-size: 22px;
  }

  .map-container {
    padding: 20px;
  }

  .map-placeholder {
    padding: 30px 15px;
  }

  .map-icon {
    font-size: 30px;
  }

  .map-placeholder p {
    font-size: 16px;
  }

  .quick-contact-item {
    padding: 15px;
    gap: 12px;
  }

  .quick-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .quick-contact-item h4 {
    font-size: 16px;
  }

  .quick-contact-item p {
    font-size: 12px;
  }

  .social-media {
    padding: 20px;
  }

  .social-links {
    gap: 8px;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}
