import { useContext, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { StoreContext } from "../../context/StoreContext";
import "./PlaceOrder.css";

const PlaceOrder = () => {
  const { token, userId } = useContext(StoreContext); // Removed 'url' as it doesn't exist in context
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (!userId && !token) {
      alert("⚠️ You are not logged in. Please log in first.");
      navigate("/"); // Navigate to home instead of non-existent /login route
    }
  }, [userId, token, navigate]);

  const { orderItems = [], subtotal = 0, totalAmount = 0, deliveryFee = 0 } =
    location.state || {};

  const [data, setData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    street: "",
    city: "",
    state: "",
    zip: "",
    country: "",
    phone: "",
  });

  const [paymentMethod, setPaymentMethod] = useState("");
  const [paymentDetails, setPaymentDetails] = useState({
    cardNumber: "",
    cardHolder: "",
    expiryDate: "",
    cvv: "",
    upiId: "",
    paypalEmail: "",
  });

  const onChangeHandler = (event) => {
    const { name, value } = event.target;
    setData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handlePaymentChange = (event) => {
    setPaymentMethod(event.target.value);
  };

  const handlePaymentDetailChange = (event) => {
    const { name, value } = event.target;
    setPaymentDetails((prevDetails) => ({ ...prevDetails, [name]: value }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!userId) {
      alert("⚠️ You are not logged in. Please log in to place an order.");
      return;
    }
    if (!data.street || !data.city || !data.state || !data.country || !data.zip) {
      alert("❌ Please fill out all address fields.");
      return;
    }
    if (!paymentMethod) {
      alert("❌ Please select a payment method.");
      return;
    }
    alert(`✅ Payment successful via ${paymentMethod}!`);
    navigate("/myorders"); // Fixed route to match App.jsx
  };

  return (
    <div className="place-order-container">
      <form onSubmit={handleSubmit} className="place-order">
        <div className="place-order-left">
          <h2>Delivery Information</h2>
          {Object.entries(data).map(([key, value]) => (
            <input
              key={key}
              type="text"
              name={key}
              placeholder={key.charAt(0).toUpperCase() + key.slice(1)}
              required
              value={value}
              onChange={onChangeHandler}
            />
          ))}
        </div>

        <div className="place-order-right">
          <h2>Order Summary</h2>
          <p>Subtotal: Rs {subtotal.toFixed(2)}</p>
          <p>Delivery Fee: Rs {deliveryFee.toFixed(2)}</p>
          <hr />
          <p><b>Total: Rs {totalAmount.toFixed(2)}</b></p>

          <h3>Payment Method</h3>
          <select onChange={handlePaymentChange} value={paymentMethod}>
            <option value="">-- Select Payment Method --</option>
            <option value="credit_card">Credit Card</option>
            <option value="upi">UPI</option>
            <option value="paypal">PayPal</option>
            <option value="cod">Cash on Delivery</option>
          </select>

          {paymentMethod === "credit_card" && (
            <div className="payment-details">
              <input type="text" name="cardNumber" placeholder="Card Number" onChange={handlePaymentDetailChange} />
              <input type="text" name="cardHolder" placeholder="Card Holder" onChange={handlePaymentDetailChange} />
              <input type="text" name="expiryDate" placeholder="Expiry Date (MM/YY)" onChange={handlePaymentDetailChange} />
              <input type="password" name="cvv" placeholder="CVV" onChange={handlePaymentDetailChange} />
            </div>
          )}

          {paymentMethod === "upi" && (
            <div className="upi-section">
              <img src="/images/upi-qr.png" alt="UPI QR Code" className="qr-code" />
              <input type="text" name="upiId" placeholder="Enter UPI ID" onChange={handlePaymentDetailChange} />
            </div>
          )}

          {paymentMethod === "paypal" && (
            <div className="payment-details">
              <input type="email" name="paypalEmail" placeholder="PayPal Email" onChange={handlePaymentDetailChange} />
            </div>
          )}

          <button type="submit">Pay Rs {totalAmount.toFixed(2)}</button>
        </div>
      </form>
    </div>
  );
};

export default PlaceOrder;
