/* General container for the place order page */
.place-order-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 10px;
    min-height: 80vh;
}

/* Order form container */
.place-order {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    max-width: 1000px;
    width: 100%;
    margin: auto;
    padding: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* Left section - Delivery Information */
.place-order-left {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
}

.place-order h2 {
    margin-bottom: 15px;
    text-align: center;
}

.place-order input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease-in-out;
}

.place-order input:focus {
    border-color: #f44336;
    outline: none;
}

/* Right section - Order Summary */
.place-order-right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 15px;
    border-left: 1px solid #ddd;
    flex: 1;
    text-align: center;
}

.place-order-right p {
    font-size: 16px;
    margin: 5px 0;
}

.place-order-right hr {
    border: none;
    height: 1px;
    background-color: #ddd;
    margin: 10px 0;
}

.place-order-right button {
    margin-top: 15px;
    padding: 10px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease-in-out;
}

.place-order-right button:hover {
    background-color: #d32f2f;
}

/* Payment Popup */
.payment-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    padding: 20px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    text-align: center;
}

.payment-popup h3 {
    margin-bottom: 15px;
}

.payment-popup select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    border: 1px solid #ccc;
}

.payment-popup button {
    padding: 10px;
    width: 100%;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
}

.payment-popup button:first-of-type {
    background-color: #4CAF50;
    color: white;
}

.payment-popup button:first-of-type:hover {
    background-color: #388E3C;
}

.payment-popup button:last-of-type {
    background-color: #ccc;
}

.payment-popup button:last-of-type:hover {
    background-color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .place-order-container {
        padding: 15px 8px;
    }

    .place-order {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 20px 15px;
        margin: 0 5px;
    }

    .place-order h2 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .place-order input,
    .place-order select {
        padding: 14px 12px;
        font-size: 16px;
        min-height: 44px;
        border-radius: 8px;
    }

    .place-order-right {
        border-left: none;
        border-top: 2px solid #f0f0f0;
        padding-top: 20px;
        order: -1; /* Move order summary to top on mobile */
    }

    .place-order-right p {
        font-size: 16px;
        margin: 8px 0;
    }

    .place-order-right button {
        padding: 14px 20px;
        font-size: 16px;
        min-height: 48px;
        border-radius: 8px;
        margin-top: 20px;
    }

    .payment-details input,
    .upi-section input {
        padding: 14px 12px;
        font-size: 16px;
        min-height: 44px;
    }

    .qr-code {
        max-width: 200px;
        height: auto;
        margin: 15px auto;
        display: block;
    }
}

@media (max-width: 480px) {
    .place-order-container {
        padding: 10px 5px;
    }

    .place-order {
        padding: 15px 10px;
        margin: 0 2px;
        border-radius: 8px;
    }

    .place-order h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .place-order input,
    .place-order select {
        padding: 12px 10px;
        font-size: 15px;
        margin-bottom: 12px;
    }

    .place-order-right p {
        font-size: 15px;
        margin: 6px 0;
    }

    .place-order-right button {
        padding: 12px 18px;
        font-size: 15px;
        min-height: 44px;
    }

    .payment-details {
        gap: 12px;
    }

    .payment-details input {
        padding: 12px 10px;
        font-size: 15px;
    }

    .qr-code {
        max-width: 150px;
        margin: 10px auto;
    }
}

@media (max-width: 360px) {
    .place-order-container {
        padding: 8px 3px;
    }

    .place-order {
        padding: 12px 8px;
        gap: 20px;
    }

    .place-order h2 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .place-order input,
    .place-order select {
        padding: 10px 8px;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .place-order-right p {
        font-size: 14px;
        margin: 5px 0;
    }

    .place-order-right button {
        padding: 10px 15px;
        font-size: 14px;
        min-height: 40px;
    }

    .qr-code {
        max-width: 120px;
    }
}
.payment-fields {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.payment-fields input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.payment-fields input:focus {
    border-color: #f44336;
    outline: none;
}
