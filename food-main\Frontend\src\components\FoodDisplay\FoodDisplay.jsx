import { useContext, useState } from 'react';
import { StoreContext } from '../../context/StoreContext';
import FoodItem from '../FoodItem/FoodItem';
import './FoodDisplay.css';

const FoodDisplay = ({ category }) => {
  const { food_list } = useContext(StoreContext);
  const [foodTypeFilter, setFoodTypeFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");

  // Ensure food_list is defined and is an array
  if (!food_list || !Array.isArray(food_list)) {
    return (
      <div className="food-display">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading delicious food items...</p>
        </div>
      </div>
    );
  }

  // Filter food items
  const filteredFoodList = food_list.filter(item => {
    const matchesCategory = category === "All" || category === item.category;
    const itemFoodType = item.foodType || "Vegetarian"; // Default to Vegetarian if not set
    const matchesFoodType = foodTypeFilter === "All" || itemFoodType === foodTypeFilter;
    const matchesSearch = searchTerm === "" ||
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesFoodType && matchesSearch;
  });

  // Get food type counts (handle missing foodType)
  const vegCount = food_list.filter(item => (item.foodType || "Vegetarian") === "Vegetarian").length;
  const nonVegCount = food_list.filter(item => (item.foodType || "Vegetarian") === "Non-Vegetarian").length;

  return (
    <div className="food-display">
      <div className="food-display-header">
        <div className="header-content">
          <h2>🍽️ Our Delicious Menu</h2>
          <p>Discover amazing dishes crafted with love and fresh ingredients</p>
        </div>

        <div className="food-stats">
          <div className="stat-item">
            <span className="stat-number">{food_list.length}</span>
            <span className="stat-label">Total Items</span>
          </div>
          <div className="stat-item veg">
            <span className="stat-number">{vegCount}</span>
            <span className="stat-label">🌱 Vegetarian</span>
          </div>
          <div className="stat-item non-veg">
            <span className="stat-number">{nonVegCount}</span>
            <span className="stat-label">🍖 Non-Veg</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="menu-controls">
        <div className="search-container">
          <div className="search-input-wrapper">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="Search for your favorite dish..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => setSearchTerm("")}
              >
                ✕
              </button>
            )}
          </div>
        </div>

        <div className="food-type-filter">
          <div className="filter-buttons">
            <button
              className={`filter-btn ${foodTypeFilter === "All" ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter("All")}
            >
              <span>🍽️ All</span>
              <span className="count-badge">{food_list.length}</span>
            </button>
            <button
              className={`filter-btn veg-btn ${foodTypeFilter === "Vegetarian" ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter("Vegetarian")}
            >
              <span>🌱 Vegetarian</span>
              <span className="count-badge">{vegCount}</span>
            </button>
            <button
              className={`filter-btn non-veg-btn ${foodTypeFilter === "Non-Vegetarian" ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter("Non-Vegetarian")}
            >
              <span>🍖 Non-Veg</span>
              <span className="count-badge">{nonVegCount}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Results Info */}
      {(searchTerm || foodTypeFilter !== "All") && (
        <div className="results-info">
          <span className="results-text">
            {filteredFoodList.length === 0
              ? "No items found"
              : `Showing ${filteredFoodList.length} of ${food_list.length} items`
            }
            {searchTerm && ` for "${searchTerm}"`}
            {foodTypeFilter !== "All" && ` (${foodTypeFilter})`}
          </span>
          {(searchTerm || foodTypeFilter !== "All") && (
            <button
              className="clear-filters-btn"
              onClick={() => {
                setSearchTerm("");
                setFoodTypeFilter("All");
              }}
            >
              Clear Filters
            </button>
          )}
        </div>
      )}

      <div className="food-display-list">
        {filteredFoodList.length > 0 ? (
          filteredFoodList.map(item => (
            <FoodItem
              key={item._id}
              id={item._id}
              name={item.name}
              description={item.description}
              price={item.price}
              image={item.image}
              foodType={item.foodType}
            />
          ))
        ) : (
          <div className="no-items-container">
            <div className="no-items-icon">🍽️</div>
            <h3>No items found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <button
              className="reset-btn"
              onClick={() => {
                setSearchTerm("");
                setFoodTypeFilter("All");
              }}
            >
              Reset Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FoodDisplay;
