import { useContext, useState } from 'react';
import { StoreContext } from '../../context/StoreContext';
import FoodItem from '../FoodItem/FoodItem';
import './FoodDisplay.css';

const FoodDisplay = ({ category }) => {
  const { food_list } = useContext(StoreContext);
  const [foodTypeFilter, setFoodTypeFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");

  // Ensure food_list is defined and is an array
  if (!food_list || !Array.isArray(food_list)) {
    return (
      <div className="food-display">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading delicious food items...</p>
        </div>
      </div>
    );
  }

  // Filter food items
  const filteredFoodList = food_list.filter(item => {
    const matchesCategory = category === "All" || category === item.category;
    const itemFoodType = item.foodType || "Vegetarian"; // Default to Vegetarian if not set
    const matchesFoodType = foodTypeFilter === "All" || itemFoodType === foodTypeFilter;
    const matchesSearch = searchTerm === "" ||
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesFoodType && matchesSearch;
  });

  // Get food type counts (handle missing foodType)
  const vegCount = food_list.filter(item => (item.foodType || "Vegetarian") === "Vegetarian").length;
  const nonVegCount = food_list.filter(item => (item.foodType || "Vegetarian") === "Non-Vegetarian").length;

  return (
    <div className="modern-food-display">
      {/* Modern Hero Section */}
      <div className="food-display-hero">
        <div className="hero-background">
          <div className="hero-pattern"></div>
        </div>
        <div className="hero-content">
          <div className="hero-badge">
            <span className="hero-badge-icon">🍽️</span>
            <span className="hero-badge-text">Fresh & Delicious</span>
          </div>
          <h1 className="hero-title">Our Complete Menu Collection</h1>
          <p className="hero-description">
            Explore our carefully curated selection of dishes, each prepared with premium ingredients
            and authentic flavors that will tantalize your taste buds
          </p>
          <div className="hero-metrics">
            <div className="metric-card">
              <div className="metric-icon">🍕</div>
              <div className="metric-info">
                <span className="metric-number">{food_list.length}</span>
                <span className="metric-label">Total Dishes</span>
              </div>
            </div>
            <div className="metric-card veg-metric">
              <div className="metric-icon">🌱</div>
              <div className="metric-info">
                <span className="metric-number">{vegCount}</span>
                <span className="metric-label">Vegetarian</span>
              </div>
            </div>
            <div className="metric-card non-veg-metric">
              <div className="metric-icon">🍖</div>
              <div className="metric-info">
                <span className="metric-number">{nonVegCount}</span>
                <span className="metric-label">Non-Vegetarian</span>
              </div>
            </div>
            <div className="metric-card rating-metric">
              <div className="metric-icon">⭐</div>
              <div className="metric-info">
                <span className="metric-number">4.8</span>
                <span className="metric-label">Avg Rating</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Control Panel */}
      <div className="modern-control-panel">
        <div className="control-header">
          <h3 className="control-title">Find Your Perfect Dish</h3>
          <p className="control-subtitle">Use our smart filters to discover exactly what you're craving</p>
        </div>

        <div className="control-grid">
          {/* Advanced Search */}
          <div className="advanced-search-section">
            <div className="search-label">
              <span className="search-label-icon">🔍</span>
              <span className="search-label-text">Search Menu</span>
            </div>
            <div className="advanced-search-box">
              <div className="search-input-container">
                <span className="search-prefix-icon">🔍</span>
                <input
                  type="text"
                  placeholder="Search by name, ingredient, or cuisine type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="advanced-search-input"
                />
                {searchTerm && (
                  <button
                    className="advanced-clear-btn"
                    onClick={() => setSearchTerm("")}
                  >
                    <span>✕</span>
                  </button>
                )}
              </div>
              <div className="search-suggestions-bar">
                <span className="suggestion-label">Popular:</span>
                <button className="suggestion-chip" onClick={() => setSearchTerm("pizza")}>Pizza</button>
                <button className="suggestion-chip" onClick={() => setSearchTerm("burger")}>Burger</button>
                <button className="suggestion-chip" onClick={() => setSearchTerm("salad")}>Salad</button>
              </div>
            </div>
          </div>

          {/* Smart Filters */}
          <div className="smart-filters-section">
            <div className="filter-label">
              <span className="filter-label-icon">🎯</span>
              <span className="filter-label-text">Dietary Preferences</span>
            </div>
            <div className="smart-filter-grid">
              <button
                className={`smart-filter-card ${foodTypeFilter === "All" ? 'active' : ''}`}
                onClick={() => setFoodTypeFilter("All")}
              >
                <div className="filter-card-icon">🍽️</div>
                <div className="filter-card-content">
                  <span className="filter-card-title">All Items</span>
                  <span className="filter-card-count">{food_list.length} dishes</span>
                </div>
                <div className="filter-card-check">✓</div>
              </button>

              <button
                className={`smart-filter-card veg-card ${foodTypeFilter === "Vegetarian" ? 'active' : ''}`}
                onClick={() => setFoodTypeFilter("Vegetarian")}
              >
                <div className="filter-card-icon">🌱</div>
                <div className="filter-card-content">
                  <span className="filter-card-title">Vegetarian</span>
                  <span className="filter-card-count">{vegCount} dishes</span>
                </div>
                <div className="filter-card-check">✓</div>
              </button>

              <button
                className={`smart-filter-card non-veg-card ${foodTypeFilter === "Non-Vegetarian" ? 'active' : ''}`}
                onClick={() => setFoodTypeFilter("Non-Vegetarian")}
              >
                <div className="filter-card-icon">🍖</div>
                <div className="filter-card-content">
                  <span className="filter-card-title">Non-Vegetarian</span>
                  <span className="filter-card-count">{nonVegCount} dishes</span>
                </div>
                <div className="filter-card-check">✓</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Results Header */}
      <div className="modern-results-section">
        <div className="results-header">
          <div className="results-info">
            <h3 className="results-title">
              {filteredFoodList.length === 0 ? "No dishes found" : "Available Dishes"}
            </h3>
            <p className="results-description">
              {filteredFoodList.length === 0
                ? "Try adjusting your search or filter criteria"
                : `Showing ${filteredFoodList.length} of ${food_list.length} delicious options`
              }
              {searchTerm && ` for "${searchTerm}"`}
              {foodTypeFilter !== "All" && ` in ${foodTypeFilter} category`}
            </p>
          </div>

          {(searchTerm || foodTypeFilter !== "All") && (
            <div className="results-actions">
              <button
                className="modern-clear-btn"
                onClick={() => {
                  setSearchTerm("");
                  setFoodTypeFilter("All");
                }}
              >
                <span className="clear-icon">🔄</span>
                <span className="clear-text">Reset All Filters</span>
              </button>
            </div>
          )}
        </div>

        {/* Modern Food Grid */}
        <div className="modern-food-grid">
          {filteredFoodList.length > 0 ? (
            filteredFoodList.map(item => (
              <div key={item._id} className="modern-food-wrapper">
                <FoodItem
                  id={item._id}
                  name={item.name}
                  description={item.description}
                  price={item.price}
                  image={item.image}
                  foodType={item.foodType}
                />
              </div>
            ))
          ) : (
            <div className="modern-empty-state">
              <div className="empty-state-visual">
                <div className="empty-plate-animation">
                  <div className="plate">🍽️</div>
                  <div className="search-icon-float">🔍</div>
                </div>
              </div>
              <div className="empty-state-content">
                <h3 className="empty-state-title">No dishes match your criteria</h3>
                <p className="empty-state-description">
                  Don't worry! We have many delicious options. Try adjusting your search
                  terms or browse our complete menu collection.
                </p>
                <div className="empty-state-actions">
                  <button
                    className="primary-reset-btn"
                    onClick={() => {
                      setSearchTerm("");
                      setFoodTypeFilter("All");
                    }}
                  >
                    <span className="reset-icon">🔄</span>
                    <span className="reset-text">Show All Dishes</span>
                  </button>
                  <button className="browse-categories-btn">
                    <span className="browse-icon">📋</span>
                    <span className="browse-text">Browse Categories</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FoodDisplay;
