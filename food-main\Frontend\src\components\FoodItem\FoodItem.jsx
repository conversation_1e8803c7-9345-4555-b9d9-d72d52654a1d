import { faHeart as regularHeart } from "@fortawesome/free-regular-svg-icons";
import { faHeart as solidHeart } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { assets } from "../../assets/assets";
import { StoreContext } from "../../context/StoreContext";
import "./FoodItem.css";

const FoodItem = ({ id, name, price, description, image, foodType = "Vegetarian" }) => {
  const { addToCart } = useContext(StoreContext);
  const navigate = useNavigate();
  const [liked, setLiked] = useState(false); // Track like status

  // Handle adding to cart
  const handleAddToCart = (e) => {
    e.stopPropagation(); // Prevent triggering navigation
    addToCart(id);
  };

  // Handle navigation to FoodDetail page
  const handleNavigate = () => {
    navigate(`/food/${id}`);
  };

  // Toggle like status
  const toggleLike = (e) => {
    e.stopPropagation(); // Prevent triggering navigation
    setLiked((prev) => !prev);
  };

  return (
    <div className="Food-item-container" onClick={handleNavigate}>
      {/* Like Icon at Top Right Corner */}
      <FontAwesomeIcon
        icon={liked ? solidHeart : regularHeart}
        className={`like-icon ${liked ? "liked" : ""}`}
        onClick={toggleLike}
      />

      <div className="Food-item-img-container">
        <img
          className="Food-item-image"
          src={`http://localhost:8889/uploads/${image}`}
          alt={name}
          onError={(e) => {
            e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80";
          }}
        />
        {/* Food Type Indicator */}
        <div className={`food-type-indicator ${(foodType || "Vegetarian") === "Vegetarian" ? "veg" : "non-veg"}`}>
          <span className="food-type-icon">
            {(foodType || "Vegetarian") === "Vegetarian" ? "🌱" : "🍖"}
          </span>
          <span className="food-type-text">{foodType || "Vegetarian"}</span>
        </div>
      </div>

      <div className="Food-item-info">
        <div className="Food-item-name-rating">
          <p className="Food-item-price-per-plate">₹{price} per plate</p>
          <p className="Food-item-name">{name}</p>
          <p className="Food-item-desc">{description}</p>
          <img src={assets.rating_starts} alt="Rating Stars" />
        </div>

        <span
          className="see-more"
          onClick={(e) => {
            e.stopPropagation();
            navigate(`/food/${id}`);
          }}
        >
          See More
        </span>

        {/* Add to Cart Button */}
        <div className="Food-item-cart-actions">
          <button className="btn btn-primary" onClick={handleAddToCart}>
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
};

export default FoodItem;
