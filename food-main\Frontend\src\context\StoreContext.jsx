import axios from "axios";
import { createContext, useEffect, useState } from "react";

// JWT decode function (simple implementation)
const jwtDecode = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
};

const API_URL = "http://localhost:8889"; // ✅ Ensure this matches your backend

export const StoreContext = createContext(null);

const StoreContextProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);  // Changed to array to match backend structure
  const [token, setToken] = useState(null);
  const [userId, setUserId] = useState(null); // ✅ Add userId state
  const [food_list, setFoodList] = useState([]);

  // Fetch food list from API
  const fetchFoodList = async () => {
    try {
      const response = await axios.get(`${API_URL}/api/food/list`);
      setFoodList(response.data?.data || []); // ✅ Ensure 'data' exists in response
    } catch (error) {
      console.error("Error fetching food list:", error.message);
    }
  };

  // Load authentication data from localStorage on app start
  const loadAuthData = () => {
    const savedToken = localStorage.getItem("token");
    const savedUserId = localStorage.getItem("userId");

    if (savedToken) {
      try {
        // Verify token is not expired
        const decodedToken = jwtDecode(savedToken);
        const currentTime = Date.now() / 1000;

        if (decodedToken.exp > currentTime) {
          setToken(savedToken);
          if (savedUserId) {
            setUserId(savedUserId);
          }
          console.log("✅ Authentication restored from localStorage");
        } else {
          // Token expired, clear storage
          localStorage.removeItem("token");
          localStorage.removeItem("userId");
          console.log("⚠️ Token expired, cleared from localStorage");
        }
      } catch (error) {
        // Invalid token, clear storage
        localStorage.removeItem("token");
        localStorage.removeItem("userId");
        console.log("❌ Invalid token, cleared from localStorage");
      }
    }
  };

  // Load initial data and check token
  useEffect(() => {
    const loadData = async () => {
      await fetchFoodList();
      const storedToken = localStorage.getItem("token");
      const storedUserId = localStorage.getItem("userId"); // ✅ Retrieve userId from storage

      if (storedToken) {
        setToken(storedToken);
        console.log("User Token from Storage:", storedToken);
      }

      if (storedUserId) {
        setUserId(storedUserId);
        console.log("User ID from Storage:", storedUserId);
      }
    };
    loadData();
  }, []);

  // ✅ Define addToCart before returning context
  const addToCart = async (itemId, quantity = 1) => {
    if (!token) {
      console.error("No token found. User is not authenticated.");
      return;
    }

    try {
      const decodedToken = jwtDecode(token);
      const userId = decodedToken?.id; // ✅ Ensure 'id' is present in JWT

      if (!userId) {
        console.error("User ID is missing in token");
        return;
      }

      console.log("Adding to cart:", { userId, itemId, quantity });

      const response = await axios.post(
        `${API_URL}/api/cart/add`,
        { userId, itemId, quantity },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      console.log("Item added to cart:", response.data);

      setCartItems((prev) => ({
        ...prev,
        [itemId]: (prev[itemId] || 0) + quantity,
      }));
    } catch (error) {
      console.error(
        "Error adding to cart:",
        error.response?.data?.message || error.message
      );
    }
  };

  // ✅ Define removeFromCart function
  const removeFromCart = (itemId) => {
    setCartItems((prev) => {
      const updatedCart = { ...prev };
      delete updatedCart[itemId];
      return updatedCart;
    });
  };

  // ✅ Define getTotalCartAmount function
  const getTotalCartAmount = () => {
    return Object.keys(cartItems).reduce((total, itemId) => {
      const item = food_list.find((food) => food._id === itemId);
      return total + (item ? item.price * cartItems[itemId] : 0);
    }, 0);
  };

  // Load data on component mount
  useEffect(() => {
    loadAuthData(); // Load authentication data first
    fetchFoodList(); // Then load food list
  }, []);

  return (
    <StoreContext.Provider
      value={{
        cartItems,
        setCartItems,
        addToCart, // ✅ Now correctly defined
        removeFromCart,
        getTotalCartAmount,
        token,
        setToken,
        userId,
        setUserId,
        food_list,
        API_URL,
      }}
    >
      {children}
    </StoreContext.Provider>
  );
};

export default StoreContextProvider;
