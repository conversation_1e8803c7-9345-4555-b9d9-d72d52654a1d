import { useContext, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { StoreContext } from "../../context/StoreContext";
import "./FoodDetail.css";

const FoodDetail = () => {
  const { food_list = [], addToCart, cartItems } = useContext(StoreContext);
  const { id } = useParams();
  const navigate = useNavigate();

  if (!Array.isArray(food_list)) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">🍽️</div>
        <p>Loading delicious details...</p>
      </div>
    );
  }

  const foodItem = food_list.find((item) => item._id === id);

  if (!foodItem) {
    return (
      <div className="not-found-container">
        <div className="not-found-icon">😔</div>
        <h2>Food Item Not Found</h2>
        <p>The item you're looking for doesn't exist or has been removed.</p>
        <button className="back-btn" onClick={() => navigate('/')}>
          ← Back to Menu
        </button>
      </div>
    );
  }

  const [showFullDesc, setShowFullDesc] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addToCart(id);
    }
    setQuantity(1);
  };

  const currentQuantityInCart = cartItems[id] || 0;
  const totalPrice = foodItem.price * quantity;
  const originalPrice = Math.floor(foodItem.price * 1.2);
  const discount = Math.floor(((originalPrice - foodItem.price) / originalPrice) * 100);

  // Sample images for carousel (in real app, these would come from the backend)
  const foodImages = [
    foodItem.image ? `http://localhost:8889/uploads/${foodItem.image}` : "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    "https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  ];

  return (
    <div className="food-detail-container">
      {/* Hero Section */}
      <div className="hero-section">
        <div className="hero-background">
          <img
            src={foodImages[selectedImage]}
            alt={foodItem.name}
            className="hero-bg-image"
            onError={(e) => {
              e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80";
            }}
          />
          <div className="hero-overlay"></div>
        </div>

        <div className="hero-content">
          <button className="floating-back-btn" onClick={() => navigate('/')}>
            <span className="back-arrow">←</span>
          </button>

          <div className="hero-info">
            <div className="food-category-chip">
              <span className="category-icon">🍽️</span>
              <span>{foodItem.category || 'Delicious Food'}</span>
            </div>

            <h1 className="hero-title">{foodItem.name}</h1>

            <div className="hero-meta">
              <div className="rating-chip">
                <span className="star-icon">⭐</span>
                <span className="rating-value">4.{Math.floor(Math.random() * 5) + 5}</span>
                <span className="rating-count">(128+ reviews)</span>
              </div>

              <div className={`diet-badge ${(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? 'veg' : 'non-veg'}`}>
                <span className="diet-icon">
                  {(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? '🌱' : '🍖'}
                </span>
                <span className="diet-text">
                  {(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? 'Pure Veg' : 'Non-Veg'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        <div className="content-grid">
          {/* Left Column - Details */}
          <div className="details-column">
            {/* Price Card */}
            <div className="price-card">
              <div className="price-header">
                <div className="current-price">₹{foodItem.price}</div>
                <div className="price-details">
                  <span className="original-price">₹{originalPrice}</span>
                  <span className="discount-tag">{discount}% OFF</span>
                </div>
              </div>
              <div className="price-note">Inclusive of all taxes</div>
            </div>

            {/* Description Card */}
            <div className="description-card">
              <h3 className="card-title">About This Dish</h3>
              <p className="description-text">
                {showFullDesc
                  ? foodItem.description || "A delicious dish prepared with fresh ingredients and authentic flavors that will tantalize your taste buds."
                  : `${(foodItem.description || "A delicious dish prepared with fresh ingredients and authentic flavors that will tantalize your taste buds.").slice(0, 120)}${(foodItem.description || "").length > 120 ? '...' : ''}`}
              </p>
              {(foodItem.description || "").length > 120 && (
                <button
                  className="read-more-btn"
                  onClick={() => setShowFullDesc(!showFullDesc)}
                >
                  {showFullDesc ? 'Show Less' : 'Read More'}
                </button>
              )}
            </div>

            {/* Features Card */}
            <div className="features-card">
              <h3 className="card-title">What Makes It Special</h3>
              <div className="features-list">
                <div className="feature-item">
                  <span className="feature-icon">⚡</span>
                  <div className="feature-content">
                    <span className="feature-title">Quick Delivery</span>
                    <span className="feature-desc">Ready in 15-20 minutes</span>
                  </div>
                </div>
                <div className="feature-item">
                  <span className="feature-icon">👨‍🍳</span>
                  <div className="feature-content">
                    <span className="feature-title">Chef's Special</span>
                    <span className="feature-desc">Handcrafted with love</span>
                  </div>
                </div>
                <div className="feature-item">
                  <span className="feature-icon">🌿</span>
                  <div className="feature-content">
                    <span className="feature-title">Fresh Ingredients</span>
                    <span className="feature-desc">Sourced daily</span>
                  </div>
                </div>
                <div className="feature-item">
                  <span className="feature-icon">📦</span>
                  <div className="feature-content">
                    <span className="feature-title">Safe Packaging</span>
                    <span className="feature-desc">Hygienic & secure</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Order */}
          <div className="order-column">
            {/* Image Gallery */}
            <div className="image-gallery">
              <div className="main-image-wrapper">
                <img
                  src={foodImages[selectedImage]}
                  alt={foodItem.name}
                  className="gallery-main-image"
                  onError={(e) => {
                    e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80";
                  }}
                />
                <div className="image-counter">
                  <span>{selectedImage + 1} / {foodImages.length}</span>
                </div>
              </div>

              <div className="thumbnail-strip">
                {foodImages.map((image, index) => (
                  <button
                    key={index}
                    className={`thumbnail-btn ${selectedImage === index ? 'active' : ''}`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img
                      src={image}
                      alt={`View ${index + 1}`}
                      className="thumbnail-image"
                      onError={(e) => {
                        e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80";
                      }}
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Order Card */}
            <div className="order-card">
              <div className="order-header">
                <h3 className="order-title">Customize Your Order</h3>
                <div className="serving-info">
                  <span className="serving-icon">👥</span>
                  <span>Perfect for 1-2 people</span>
                </div>
              </div>

              <div className="quantity-section">
                <label className="quantity-label">Quantity</label>
                <div className="quantity-selector">
                  <button
                    className="qty-control minus"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                  >
                    −
                  </button>
                  <span className="qty-value">{quantity}</span>
                  <button
                    className="qty-control plus"
                    onClick={() => setQuantity(quantity + 1)}
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="order-summary">
                <div className="summary-row">
                  <span className="summary-label">Item Total</span>
                  <span className="summary-value">₹{totalPrice}</span>
                </div>
                <div className="summary-row">
                  <span className="summary-label">Delivery Fee</span>
                  <span className="summary-value free">FREE</span>
                </div>
                <div className="summary-row total">
                  <span className="summary-label">Total Amount</span>
                  <span className="summary-value">₹{totalPrice}</span>
                </div>
                {currentQuantityInCart > 0 && (
                  <div className="cart-note">
                    <span className="cart-icon">🛒</span>
                    <span>{currentQuantityInCart} already in cart</span>
                  </div>
                )}
              </div>

              <div className="order-actions">
                <button className="add-to-cart-primary" onClick={handleAddToCart}>
                  <span className="btn-icon">🛒</span>
                  <span className="btn-text">Add {quantity} to Cart</span>
                </button>

                <div className="secondary-actions">
                  <button className="action-btn favorite">
                    <span className="btn-icon">❤️</span>
                    <span>Save</span>
                  </button>
                  <button className="action-btn share">
                    <span className="btn-icon">📤</span>
                    <span>Share</span>
                  </button>
                </div>
              </div>

              <div className="delivery-info">
                <div className="delivery-item">
                  <span className="delivery-icon">🚚</span>
                  <div className="delivery-text">
                    <span className="delivery-title">Free Delivery</span>
                    <span className="delivery-desc">On orders above ₹199</span>
                  </div>
                </div>
                <div className="delivery-item">
                  <span className="delivery-icon">⏰</span>
                  <div className="delivery-text">
                    <span className="delivery-title">Quick Service</span>
                    <span className="delivery-desc">15-20 minutes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodDetail;
