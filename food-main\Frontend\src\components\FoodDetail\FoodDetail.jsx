import { useContext, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { StoreContext } from "../../context/StoreContext";
import "./FoodDetail.css";

const FoodDetail = () => {
  const { food_list = [], addToCart, cartItems } = useContext(StoreContext);
  const { id } = useParams();
  const navigate = useNavigate();

  if (!Array.isArray(food_list)) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">🍽️</div>
        <p>Loading delicious details...</p>
      </div>
    );
  }

  const foodItem = food_list.find((item) => item._id === id);

  if (!foodItem) {
    return (
      <div className="not-found-container">
        <div className="not-found-icon">😔</div>
        <h2>Food Item Not Found</h2>
        <p>The item you're looking for doesn't exist or has been removed.</p>
        <button className="back-btn" onClick={() => navigate('/')}>
          ← Back to Menu
        </button>
      </div>
    );
  }

  const [showFullDesc, setShowFullDesc] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addToCart(id);
    }
    setQuantity(1);
  };

  const currentQuantityInCart = cartItems[id] || 0;
  const totalPrice = foodItem.price * quantity;
  const originalPrice = Math.floor(foodItem.price * 1.2);
  const discount = Math.floor(((originalPrice - foodItem.price) / originalPrice) * 100);

  // Sample images for carousel (in real app, these would come from the backend)
  const foodImages = [
    foodItem.image ? `http://localhost:8889/uploads/${foodItem.image}` : "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    "https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  ];

  return (
    <div className="modern-food-detail">
      {/* Back Button */}
      <div className="detail-header">
        <button className="back-button" onClick={() => navigate('/')}>
          <span className="back-icon">←</span>
          <span className="back-text">Back to Menu</span>
        </button>
        <div className="breadcrumb">
          <span>Home</span> <span className="separator">›</span> <span>Menu</span> <span className="separator">›</span> <span>{foodItem.name}</span>
        </div>
      </div>

      <div className="detail-content">
        {/* Image Gallery Section */}
        <div className="image-section">
          <div className="main-image-container">
            <img
              src={foodImages[selectedImage]}
              alt={foodItem.name}
              className="main-image"
              onError={(e) => {
                e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80";
              }}
            />
            <div className="image-overlay">
              <div className="food-type-badge">
                <span className={`type-indicator ${(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? 'veg' : 'non-veg'}`}>
                  {(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? '🌱' : '🍖'}
                </span>
                <span className="type-text">
                  {(foodItem.foodType || 'Vegetarian') === 'Vegetarian' ? 'Vegetarian' : 'Non-Vegetarian'}
                </span>
              </div>
            </div>
          </div>

          <div className="image-thumbnails">
            {foodImages.map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`${foodItem.name} view ${index + 1}`}
                className={`thumbnail ${selectedImage === index ? 'active' : ''}`}
                onClick={() => setSelectedImage(index)}
                onError={(e) => {
                  e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80";
                }}
              />
            ))}
          </div>
        </div>

        {/* Details Section */}
        <div className="details-section">
          <div className="product-header">
            <h1 className="product-title">{foodItem.name}</h1>
            <div className="product-meta">
              <div className="rating-section">
                <div className="stars">
                  <span className="star filled">★</span>
                  <span className="star filled">★</span>
                  <span className="star filled">★</span>
                  <span className="star filled">★</span>
                  <span className="star">★</span>
                </div>
                <span className="rating-text">4.2 (128 reviews)</span>
              </div>
              <div className="category-tag">
                <span className="category-icon">🍽️</span>
                <span>{foodItem.category || 'Main Course'}</span>
              </div>
            </div>
          </div>

          <div className="price-section">
            <div className="current-price">₹{foodItem.price}</div>
            <div className="original-price">₹{originalPrice}</div>
            <div className="discount-badge">{discount}% OFF</div>
          </div>

          <div className="description-section">
            <h3 className="section-title">Description</h3>
            <p className="description-text">
              {showFullDesc
                ? foodItem.description
                : `${foodItem.description?.slice(0, 150) || "Delicious food item prepared with fresh ingredients and authentic flavors."}${foodItem.description?.length > 150 ? '...' : ''}`}
              {foodItem.description?.length > 150 && (
                <button
                  className="toggle-desc-btn"
                  onClick={() => setShowFullDesc(!showFullDesc)}
                >
                  {showFullDesc ? 'Show Less' : 'Read More'}
                </button>
              )}
            </p>
          </div>

          <div className="features-section">
            <h3 className="section-title">Features</h3>
            <div className="features-grid">
              <div className="feature-item">
                <span className="feature-icon">⏱️</span>
                <span className="feature-text">Ready in 15-20 mins</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">👥</span>
                <span className="feature-text">Serves 1-2 people</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔥</span>
                <span className="feature-text">Freshly prepared</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📦</span>
                <span className="feature-text">Safe packaging</span>
              </div>
            </div>
          </div>

          <div className="quantity-section">
            <h3 className="section-title">Quantity</h3>
            <div className="quantity-controls">
              <button
                className="qty-btn minus"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                −
              </button>
              <span className="quantity-display">{quantity}</span>
              <button
                className="qty-btn plus"
                onClick={() => setQuantity(quantity + 1)}
              >
                +
              </button>
            </div>
            <div className="quantity-info">
              <span className="total-price">Total: ₹{totalPrice}</span>
              {currentQuantityInCart > 0 && (
                <span className="cart-info">({currentQuantityInCart} already in cart)</span>
              )}
            </div>
          </div>

          <div className="action-section">
            <button className="add-to-cart-btn" onClick={handleAddToCart}>
              <span className="cart-icon">🛒</span>
              <span className="btn-text">Add {quantity} to Cart</span>
              <span className="btn-price">₹{totalPrice}</span>
            </button>
            <div className="secondary-actions">
              <button className="wishlist-btn">
                <span className="heart-icon">❤️</span>
                <span>Add to Wishlist</span>
              </button>
              <button className="share-btn">
                <span className="share-icon">📤</span>
                <span>Share</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodDetail;
