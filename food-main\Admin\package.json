{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.7", "express": "^4.21.0", "mongoose": "^8.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.1.5", "react-toastify": "^10.0.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}