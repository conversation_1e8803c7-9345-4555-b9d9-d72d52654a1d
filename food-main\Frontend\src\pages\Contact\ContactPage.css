/* Contact Page Styles */
.contact-page {
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Contact Hero Section */
.contact-page-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  text-align: center;
}

.contact-hero-content h1 {
  font-size: max(3.5vw, 42px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.contact-hero-content p {
  font-size: max(1.2vw, 20px);
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.hero-feature {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 25px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
  font-size: 20px;
}

/* FAQ Section */
.faq-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.faq-container h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 60px 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  text-align: left;
}

.faq-item {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.faq-item h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.faq-item p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Contact Methods */
.contact-methods {
  padding: 100px 0;
  background: white;
}

.methods-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.methods-container h2 {
  font-size: max(2.5vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 60px 0;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.method-card {
  background: #f8f9fa;
  padding: 40px 30px;
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.method-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.method-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.method-card h3 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.method-card p {
  color: #7f8c8d;
  margin: 0 0 25px 0;
  line-height: 1.6;
}

.method-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.method-details a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  font-size: 18px;
}

.method-details a:hover {
  text-decoration: underline;
}

.method-details span {
  color: #95a5a6;
  font-size: 14px;
}

.chat-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.social-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
}

.social-link:hover {
  text-decoration: underline;
}

/* Emergency Contact */
.emergency-contact {
  padding: 80px 0;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  text-align: center;
}

.emergency-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.emergency-content h2 {
  font-size: max(2.5vw, 32px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.emergency-content p {
  font-size: 18px;
  margin: 0 0 30px 0;
  opacity: 0.9;
}

.emergency-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.emergency-btn {
  background: white;
  color: #e74c3c;
  padding: 18px 40px;
  border-radius: 30px;
  text-decoration: none;
  font-size: 20px;
  font-weight: 700;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.emergency-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

.emergency-note {
  font-size: 14px;
  opacity: 0.8;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .contact-page {
    padding-top: 60px;
  }

  .contact-page-hero {
    padding: 80px 0;
  }

  .contact-hero-content h1 {
    font-size: 32px;
  }

  .contact-hero-content p {
    font-size: 18px;
  }

  .hero-features {
    gap: 20px;
  }

  .hero-feature {
    padding: 12px 20px;
  }

  .faq-section,
  .contact-methods {
    padding: 80px 0;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .methods-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .method-card {
    padding: 30px 20px;
  }

  .emergency-contact {
    padding: 60px 0;
  }

  .emergency-btn {
    padding: 15px 30px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .contact-hero-content h1 {
    font-size: 28px;
  }

  .contact-hero-content p {
    font-size: 16px;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .faq-container,
  .methods-container,
  .emergency-container {
    padding: 0 15px;
  }

  .faq-container h2,
  .methods-container h2,
  .emergency-content h2 {
    font-size: 28px;
  }

  .faq-item,
  .method-card {
    padding: 25px 20px;
  }

  .emergency-btn {
    padding: 12px 25px;
    font-size: 16px;
  }
}
