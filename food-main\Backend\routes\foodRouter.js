import express from 'express';
import multer from 'multer';
import { addFood, getFoodById, listFood, removeFood } from '../controllers/foodController.js';

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'Uploads/'); // Fixed to match actual directory name
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}_${file.originalname}`); // Unique filename
  },
});

const upload = multer({ storage });
const foodRouter = express.Router();

// Routes
foodRouter.post('/add', upload.single('image'), addFood);
foodRouter.get('/list', listFood);
foodRouter.get('/:id', getFoodById); // Get food by ID
foodRouter.delete('/remove/:id', removeFood);

export default foodRouter;
