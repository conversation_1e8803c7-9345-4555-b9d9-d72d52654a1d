#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0;
  text-align: left;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Mobile-first responsive padding */
@media (max-width: 768px) {
  #root {
    padding: 0;
  }
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Improve touch targets */
button, .btn, a {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus, input:focus, select:focus, textarea:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
