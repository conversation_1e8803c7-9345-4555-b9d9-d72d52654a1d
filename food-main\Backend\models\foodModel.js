import mongoose from "mongoose";

const foodSchema = new mongoose.Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true }, // Price should be a number
    image: { type: String, required: true },
    category: { type: String, required: true },
    foodType: {
        type: String,
        required: true,
        enum: ['Vegetarian', 'Non-Vegetarian'],
        default: 'Vegetarian'
    },
});

const Food = mongoose.models.Food || mongoose.model("Food", foodSchema);
export default Food;
