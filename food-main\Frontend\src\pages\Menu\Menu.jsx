import { useState } from 'react';
import ExploreMenu from '../../components/ExploreMenu/ExploreMenu';
import FoodDisplay from '../../components/FoodDisplay/FoodDisplay';
import './Menu.css';

const Menu = () => {
  const [category, setCategory] = useState("All");

  return (
    <div className="menu-page">
      {/* Menu Header */}
      <div className="menu-header">
        <div className="menu-hero">
          <div className="menu-hero-content">
            <h1>🍽️ Our Delicious Menu</h1>
            <p>Discover our wide variety of fresh, delicious meals prepared with love and the finest ingredients</p>
            <div className="menu-stats">
              <div className="stat-card">
                <span className="stat-number">50+</span>
                <span className="stat-label">Menu Items</span>
              </div>
              <div className="stat-card">
                <span className="stat-number">8</span>
                <span className="stat-label">Categories</span>
              </div>
              <div className="stat-card">
                <span className="stat-number">100%</span>
                <span className="stat-label">Fresh</span>
              </div>
            </div>
          </div>
          <div className="menu-hero-image">
            <img 
              src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
              alt="Delicious Food"
            />
          </div>
        </div>
      </div>

      {/* Menu Categories */}
      <div className="menu-content">
        <ExploreMenu category={category} setCategory={setCategory} />
        <FoodDisplay category={category} />
      </div>

      {/* Menu Features */}
      <div className="menu-features">
        <div className="features-container">
          <h2>Why Choose Our Menu?</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🌱</div>
              <h3>Fresh Ingredients</h3>
              <p>We use only the freshest, locally sourced ingredients in all our dishes</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">👨‍🍳</div>
              <h3>Expert Chefs</h3>
              <p>Our experienced chefs craft each dish with passion and expertise</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🚚</div>
              <h3>Fast Delivery</h3>
              <p>Hot, fresh meals delivered to your door in 30-45 minutes</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">💰</div>
              <h3>Great Value</h3>
              <p>Delicious, high-quality meals at affordable prices</p>
            </div>
          </div>
        </div>
      </div>

      {/* Special Offers */}
      <div className="special-offers">
        <div className="offers-container">
          <h2>🎉 Special Offers</h2>
          <div className="offers-grid">
            <div className="offer-card">
              <div className="offer-badge">20% OFF</div>
              <h3>First Order Discount</h3>
              <p>Get 20% off on your first order. Use code: WELCOME20</p>
              <button className="offer-btn">Claim Now</button>
            </div>
            <div className="offer-card">
              <div className="offer-badge">FREE</div>
              <h3>Free Delivery</h3>
              <p>Free delivery on orders above ₹500. No minimum order required!</p>
              <button className="offer-btn">Order Now</button>
            </div>
            <div className="offer-card">
              <div className="offer-badge">COMBO</div>
              <h3>Family Combo</h3>
              <p>Special family combo meals for 4 people at discounted prices</p>
              <button className="offer-btn">View Combos</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Menu;
