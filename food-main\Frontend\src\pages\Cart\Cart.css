/* Modern Cart Styles */
.modern-cart {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    min-height: 70vh;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Cart Header */
.cart-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.cart-title {
    font-size: 36px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cart-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 80px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    margin: 40px 0;
}

.empty-cart-icon {
    font-size: 80px;
    margin-bottom: 24px;
    opacity: 0.7;
}

.empty-cart h3 {
    font-size: 28px;
    color: #374151;
    margin: 0 0 12px 0;
}

.empty-cart p {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 32px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.continue-shopping-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.continue-shopping-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Cart Items Container */
.cart-items-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

/* Cart Item Card */
.cart-item-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: grid;
    grid-template-columns: 120px 1fr auto auto;
    gap: 24px;
    align-items: center;
    transition: all 0.3s ease;
}

.cart-item-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Item Image */
.item-image {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    background: #f3f4f6;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.item-image:hover img {
    transform: scale(1.05);
}

/* Item Details */
.item-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.item-name {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.item-price {
    font-size: 18px;
    font-weight: 700;
    color: #059669;
    margin: 0;
}

.item-description {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

/* Item Controls */
.item-controls {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
}

.quantity-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.quantity-control label {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quantity-input-group {
    display: flex;
    align-items: center;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.qty-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: #e5e7eb;
    color: #1f2937;
}

.qty-input {
    width: 50px;
    height: 32px;
    border: none;
    background: transparent;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
}

.qty-input:focus {
    outline: none;
}

.item-total {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
}

.total-label {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.total-amount {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
}

/* Item Actions */
.item-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.remove-btn {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-btn:hover {
    background: #fecaca;
    border-color: #f87171;
    transform: translateY(-1px);
}

.order-single-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.order-single-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Cart Actions */
.cart-actions {
    display: flex;
    justify-content: center;
    margin: 32px 0;
}

.clear-cart-btn {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-cart-btn:hover {
    background: #fecaca;
    border-color: #f87171;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Cart Summary Card */
.cart-summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 32px;
    margin-top: 32px;
    border: 1px solid #e5e7eb;
}

.summary-header {
    text-align: center;
    margin-bottom: 24px;
}

.summary-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.summary-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.summary-details {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.summary-row:not(:last-child) {
    border-bottom: 1px solid #f3f4f6;
}

.summary-label {
    font-size: 16px;
    color: #6b7280;
}

.summary-value {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.delivery-fee {
    color: #059669;
}

.total-row {
    border-top: 2px solid #e5e7eb !important;
    padding-top: 16px;
    margin-top: 8px;
}

.total-row .summary-label {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.total-row .total-amount {
    font-size: 20px;
    font-weight: 700;
    color: #059669;
}

.summary-actions {
    text-align: center;
}

.checkout-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 16px 48px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    margin-bottom: 16px;
}

.checkout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.checkout-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.note-icon {
    font-size: 16px;
}

.note-text {
    font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
    .cart-item-card {
        grid-template-columns: 100px 1fr;
        grid-template-rows: auto auto;
        gap: 16px;
    }

    .item-image {
        width: 100px;
        height: 100px;
    }

    .item-controls {
        grid-column: 1 / -1;
        flex-direction: row;
        justify-content: space-between;
    }

    .item-actions {
        grid-column: 1 / -1;
        flex-direction: row;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .modern-cart {
        padding: 16px;
    }

    .cart-title {
        font-size: 28px;
    }

    .cart-item-card {
        grid-template-columns: 80px 1fr;
        padding: 20px;
        gap: 12px;
    }

    .item-image {
        width: 80px;
        height: 80px;
    }

    .item-name {
        font-size: 18px;
    }

    .item-price {
        font-size: 16px;
    }

    .item-controls {
        gap: 12px;
    }

    .item-actions {
        gap: 8px;
    }

    .remove-btn,
    .order-single-btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .cart-summary-card {
        padding: 24px;
    }

    .checkout-btn {
        padding: 14px 32px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .modern-cart {
        padding: 12px;
    }

    .cart-title {
        font-size: 24px;
    }

    .cart-item-card {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 16px;
    }

    .item-image {
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }

    .item-controls {
        flex-direction: column;
        gap: 16px;
    }

    .item-actions {
        flex-direction: column;
        gap: 12px;
    }

    .remove-btn,
    .order-single-btn {
        width: 100%;
        padding: 12px;
    }

    .cart-summary-card {
        padding: 20px;
    }

    .summary-header h3 {
        font-size: 20px;
    }

    .checkout-btn {
        width: 100%;
        padding: 16px;
    }
}

/* Remove old styles */
.cart-item,
.cart-items-title,
.cart-items-item,
.cart-bottom,
.cart-total-details,
.cart-promocode,
.cart-promocode-input,
.cart-summary {
    display: none;
}

.cart-item {
    display: flex;
    flex-direction: column;
}

.cart-items-title {
    display: grid;
    grid-template-columns: repeat(7, 1fr); /* Updated to match 7 columns in JSX */
    gap: 10px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

.cart-items-item {
    display: grid;
    grid-template-columns: repeat(7, 1fr); /* Updated to match 7 columns in JSX */
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    border-bottom: 1px solid #ccc; /* Optional: Add a bottom border to separate items */
}

.cart-items-item img {
    max-width: 60px;
    height: auto;
    border-radius: 5px;
}

.cart-items-item p {
    margin: 0;
    text-align: center;
}

.cart-items-item input {
    width: 60px; /* Set a fixed width for consistency */
    text-align: center; /* Center the text inside the input */
    border: 1px solid #ccc; /* Add a border to the input */
    border-radius: 5px; /* Rounded corners */
    padding: 5px; /* Add some padding */
    justify-self: center; /* Center the input in the grid cell */
}

.cart-items-item button {
    padding: 5px 10px;
    background-color: #f44336;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.cart-items-item button:hover {
    background-color: #d32f2f;
}

hr {
    margin: 15px 0;
    border: 0;
    height: 1px;
    background: #ccc;
}

/* Cart totals section */
.cart-bottom {
    margin-top: 20px;
    padding: 10px;
    border-top: 1px solid #ccc; /* Optional: Add top border for separation */
}

.cart-total-details {
    display: flex;
    justify-content: space-between; /* Aligns the labels and values on either side */
    margin: 5px 0;
}

.cart-promocode {
    margin-top: 20px;
    padding: 10px;
    border-top: 1px solid #ccc; /* Optional: Add top border for separation */
}

.cart-promocode-input {
    margin-top: 10px;
}

.cart-promocode-input input {
    width: 200px; /* Set input width */
    padding: 5px;
    text-align: center;
    font-size: large;
    background-color: #b7b3b3;
}

/* Clear Cart Button Styles */
.clear-cart-btn {
    margin: 20px 0;
    padding: 10px 20px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.clear-cart-btn:hover {
    background-color: #ff5252;
}

/* Cart Summary Styles */
.cart-summary {
    margin-top: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.cart-summary h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.cart-summary p {
    margin: 8px 0;
    font-size: 16px;
}

.cart-summary p:last-of-type {
    font-size: 18px;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ccc;
}

.cart-summary button {
    margin-top: 15px;
    padding: 12px 25px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
    width: 100%;
}

.cart-summary button:hover {
    background-color: #45a049;
}

/* Optional: Additional styles for responsiveness */
@media (max-width: 768px) {
    .cart-items-title,
    .cart-items-item {
        grid-template-columns: 1fr; /* Stack items on smaller screens */
        text-align: left;
    }

    .cart-items-item {
        margin: 15px 0; /* Adjust margin for stacked layout */
        padding: 10px;
        background-color: #f8f8f8;
        border-radius: 5px;
    }

    .cart-items-title {
        font-size: 14px; /* Adjust font size for smaller screens */
        display: none; /* Hide header on mobile since items are stacked */
    }

    .cart-total-details {
        flex-direction: column; /* Stack subtotal and delivery fee on smaller screens */
        align-items: flex-start; /* Align items to the start */
    }

    .cart-total-details p {
        width: 100%; /* Make total details full width */
    }

    .cart-promocode-input input {
        width: 100%; /* Make promo code input full width */
    }

    .cart-items-item img {
        max-width: 40px; /* Adjust image size for smaller screens */
        margin-bottom: 10px;
    }

    .cart-items-item p,
    .cart-items-item input,
    .cart-items-item button {
        margin: 5px 0;
        display: block;
        width: 100%;
    }

    .cart-items-item input {
        max-width: 80px;
    }

    .cart-items-item button {
        max-width: 120px;
        margin: 5px 5px 5px 0;
        display: inline-block;
        width: auto;
    }

    .clear-cart-btn {
        width: 100%;
        margin: 15px 0;
    }

    .cart-summary {
        margin-top: 20px;
        padding: 15px;
    }
}
