.cart {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.cart h2 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.cart > p {
    text-align: center;
    font-size: 18px;
    color: #666;
    margin: 40px 0;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 8px;
}

.cart-item {
    display: flex;
    flex-direction: column;
}

.cart-items-title {
    display: grid;
    grid-template-columns: repeat(7, 1fr); /* Updated to match 7 columns in JSX */
    gap: 10px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

.cart-items-item {
    display: grid;
    grid-template-columns: repeat(7, 1fr); /* Updated to match 7 columns in JSX */
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    border-bottom: 1px solid #ccc; /* Optional: Add a bottom border to separate items */
}

.cart-items-item img {
    max-width: 60px;
    height: auto;
    border-radius: 5px;
}

.cart-items-item p {
    margin: 0;
    text-align: center;
}

.cart-items-item input {
    width: 60px; /* Set a fixed width for consistency */
    text-align: center; /* Center the text inside the input */
    border: 1px solid #ccc; /* Add a border to the input */
    border-radius: 5px; /* Rounded corners */
    padding: 5px; /* Add some padding */
    justify-self: center; /* Center the input in the grid cell */
}

.cart-items-item button {
    padding: 5px 10px;
    background-color: #f44336;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.cart-items-item button:hover {
    background-color: #d32f2f;
}

hr {
    margin: 15px 0;
    border: 0;
    height: 1px;
    background: #ccc;
}

/* Cart totals section */
.cart-bottom {
    margin-top: 20px;
    padding: 10px;
    border-top: 1px solid #ccc; /* Optional: Add top border for separation */
}

.cart-total-details {
    display: flex;
    justify-content: space-between; /* Aligns the labels and values on either side */
    margin: 5px 0;
}

.cart-promocode {
    margin-top: 20px;
    padding: 10px;
    border-top: 1px solid #ccc; /* Optional: Add top border for separation */
}

.cart-promocode-input {
    margin-top: 10px;
}

.cart-promocode-input input {
    width: 200px; /* Set input width */
    padding: 5px;
    text-align: center;
    font-size: large;
    background-color: #b7b3b3;
}

/* Clear Cart Button Styles */
.clear-cart-btn {
    margin: 20px 0;
    padding: 10px 20px;
    background-color: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.clear-cart-btn:hover {
    background-color: #ff5252;
}

/* Cart Summary Styles */
.cart-summary {
    margin-top: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.cart-summary h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.cart-summary p {
    margin: 8px 0;
    font-size: 16px;
}

.cart-summary p:last-of-type {
    font-size: 18px;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #ccc;
}

.cart-summary button {
    margin-top: 15px;
    padding: 12px 25px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
    width: 100%;
}

.cart-summary button:hover {
    background-color: #45a049;
}

/* Optional: Additional styles for responsiveness */
@media (max-width: 768px) {
    .cart-items-title,
    .cart-items-item {
        grid-template-columns: 1fr; /* Stack items on smaller screens */
        text-align: left;
    }

    .cart-items-item {
        margin: 15px 0; /* Adjust margin for stacked layout */
        padding: 10px;
        background-color: #f8f8f8;
        border-radius: 5px;
    }

    .cart-items-title {
        font-size: 14px; /* Adjust font size for smaller screens */
        display: none; /* Hide header on mobile since items are stacked */
    }

    .cart-total-details {
        flex-direction: column; /* Stack subtotal and delivery fee on smaller screens */
        align-items: flex-start; /* Align items to the start */
    }

    .cart-total-details p {
        width: 100%; /* Make total details full width */
    }

    .cart-promocode-input input {
        width: 100%; /* Make promo code input full width */
    }

    .cart-items-item img {
        max-width: 40px; /* Adjust image size for smaller screens */
        margin-bottom: 10px;
    }

    .cart-items-item p,
    .cart-items-item input,
    .cart-items-item button {
        margin: 5px 0;
        display: block;
        width: 100%;
    }

    .cart-items-item input {
        max-width: 80px;
    }

    .cart-items-item button {
        max-width: 120px;
        margin: 5px 5px 5px 0;
        display: inline-block;
        width: auto;
    }

    .clear-cart-btn {
        width: 100%;
        margin: 15px 0;
    }

    .cart-summary {
        margin-top: 20px;
        padding: 15px;
    }
}
