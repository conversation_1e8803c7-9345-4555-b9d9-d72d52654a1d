import { useEffect, useState } from 'react';
import { NavLink } from 'react-router-dom';
import { assets } from '../../assets/assets';
import './Sidebar.css';

const Sidebar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  return (
    <>
      {/* Mobile Menu Toggle Button */}
      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileMenu}
        aria-label="Toggle navigation menu"
      >
        <span className={`hamburger ${isMobileMenuOpen ? 'open' : ''}`}>
          <span></span>
          <span></span>
          <span></span>
        </span>
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${isMobileMenuOpen ? 'mobile-open' : ''}`}>
        <div className="sidebar-content">
          <NavLink
            to="/add"
            className='sidebar-option'
            onClick={closeMobileMenu}
          >
            <img src={assets.add_icon_white} alt="Add Items" />
            <p>Add Items</p>
          </NavLink>

          <NavLink
            to="/list"
            className='sidebar-option'
            onClick={closeMobileMenu}
          >
            <img src={assets.parcel_icon} alt="List Items" />
            <p>List Items</p>
          </NavLink>

          <NavLink
            to="/orders"
            className='sidebar-option'
            onClick={closeMobileMenu}
          >
            <img src={assets.order_icon} alt="Orders" />
            <p>Orders</p>
          </NavLink>
        </div>
      </div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="mobile-overlay"
          onClick={closeMobileMenu}
        ></div>
      )}
    </>
  );
}

export default Sidebar;
