/* Admin App Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  line-height: 1.6;
}

.app-content {
  display: flex;
  min-height: calc(100vh - 60px);
  background-color: #f8f9fa;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .app-content {
    flex-direction: column;
    min-height: calc(100vh - 50px);
  }
}

@media (max-width: 480px) {
  .app-content {
    min-height: calc(100vh - 45px);
  }
}

/* Ensure main content area is responsive */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .main-content {
    padding: 15px 10px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px 8px;
  }
}

/* Responsive tables and forms */
@media (max-width: 768px) {
  table {
    font-size: 14px;
  }

  input, select, textarea, button {
    min-height: 44px;
    font-size: 16px;
  }
}

/* Touch-friendly buttons */
button {
  min-height: 44px;
  min-width: 44px;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}