/* New Modern Food Detail Styles */
.food-detail-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #1a202c;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
  display: flex;
  align-items: flex-end;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px 40px;
}

.floating-back-btn {
  position: absolute;
  top: -320px;
  left: 24px;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.floating-back-btn:hover {
  background: white;
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.back-arrow {
  font-size: 20px;
  color: #2d3748;
  font-weight: bold;
}

.hero-info {
  color: white;
}

.food-category-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.category-icon {
  font-size: 16px;
}

.hero-title {
  font-size: clamp(32px, 5vw, 48px);
  font-weight: 700;
  margin: 0 0 20px 0;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.rating-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.star-icon {
  color: #fbbf24;
  font-size: 16px;
}

.rating-value {
  font-weight: 600;
}

.rating-count {
  opacity: 0.8;
  font-size: 13px;
}

.diet-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.diet-badge.veg {
  background: rgba(34, 197, 94, 0.2);
  color: #dcfce7;
}

.diet-badge.non-veg {
  background: rgba(239, 68, 68, 0.2);
  color: #fecaca;
}

.diet-icon {
  font-size: 16px;
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  align-items: start;
}

/* Details Column */
.details-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.price-card,
.description-card,
.features-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

/* Price Card */
.price-header {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 32px;
  font-weight: 700;
  color: #059669;
}

.price-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.original-price {
  font-size: 18px;
  color: #9ca3af;
  text-decoration: line-through;
}

.discount-tag {
  background: #dc2626;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.price-note {
  color: #6b7280;
  font-size: 14px;
}

/* Description Card */
.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.description-text {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
  font-size: 16px;
}

.read-more-btn {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  font-weight: 500;
  margin-top: 8px;
  padding: 0;
  text-decoration: underline;
  font-size: 14px;
}

.read-more-btn:hover {
  color: #2563eb;
}

/* Features Card */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.feature-icon {
  font-size: 20px;
  margin-top: 2px;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.feature-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.feature-desc {
  color: #6b7280;
  font-size: 13px;
}

/* Order Column */
.order-column {
  position: sticky;
  top: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Image Gallery */
.image-gallery {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.main-image-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 12px;
}

.gallery-main-image {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-main-image:hover {
  transform: scale(1.02);
}

.image-counter {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.thumbnail-strip {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
}

.thumbnail-btn {
  flex-shrink: 0;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
  padding: 0;
}

.thumbnail-btn:hover {
  border-color: #cbd5e1;
}

.thumbnail-btn.active {
  border-color: #3b82f6;
}

.thumbnail-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  display: block;
}

/* Order Card */
.order-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.order-header {
  margin-bottom: 20px;
}

.order-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.serving-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.serving-icon {
  font-size: 16px;
}

/* Quantity Section */
.quantity-section {
  margin-bottom: 20px;
}

.quantity-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #f9fafb;
  padding: 8px 16px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  width: fit-content;
}

.qty-control {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.qty-control:hover:not(:disabled) {
  background: #f3f4f6;
  transform: scale(1.05);
}

.qty-control:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qty-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  min-width: 24px;
  text-align: center;
}

/* Order Summary */
.order-summary {
  background: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row.total {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
  margin-top: 8px;
  font-weight: 600;
}

.summary-label {
  color: #6b7280;
  font-size: 14px;
}

.summary-value {
  font-weight: 500;
  color: #1f2937;
}

.summary-value.free {
  color: #059669;
  font-weight: 600;
}

.cart-note {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px 12px;
  background: #dbeafe;
  border-radius: 8px;
  color: #1e40af;
  font-size: 13px;
}

.cart-icon {
  font-size: 14px;
}

/* Order Actions */
.order-actions {
  margin-bottom: 20px;
}

.add-to-cart-primary {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.add-to-cart-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-icon {
  font-size: 18px;
}

.secondary-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.action-btn.favorite:hover {
  border-color: #f87171;
  color: #dc2626;
}

.action-btn.share:hover {
  border-color: #60a5fa;
  color: #2563eb;
}

/* Delivery Info */
.delivery-info {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.delivery-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.delivery-item:last-child {
  margin-bottom: 0;
}

.delivery-icon {
  font-size: 20px;
  color: #059669;
}

.delivery-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.delivery-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.delivery-desc {
  color: #6b7280;
  font-size: 13px;
}

/* Loading and Error States */
.loading-container,
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px 24px;
}

.loading-spinner {
  font-size: 60px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.not-found-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.not-found-container h2 {
  font-size: 32px;
  color: #1f2937;
  margin: 0 0 15px 0;
}

.not-found-container p {
  font-size: 18px;
  color: #6b7280;
  margin: 0 0 30px 0;
}

.back-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .order-column {
    position: static;
    order: -1;
  }

  .hero-section {
    height: 50vh;
    min-height: 300px;
  }

  .floating-back-btn {
    top: -220px;
  }

  .hero-title {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 24px 16px;
  }

  .hero-content {
    padding: 0 16px 24px;
  }

  .floating-back-btn {
    top: -200px;
    left: 16px;
    width: 44px;
    height: 44px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .price-card,
  .description-card,
  .features-card,
  .order-card,
  .image-gallery {
    padding: 20px;
  }

  .current-price {
    font-size: 28px;
  }

  .gallery-main-image {
    height: 200px;
  }

  .thumbnail-image {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 20px 12px;
  }

  .hero-content {
    padding: 0 12px 20px;
  }

  .floating-back-btn {
    top: -180px;
    left: 12px;
    width: 40px;
    height: 40px;
  }

  .back-arrow {
    font-size: 18px;
  }

  .hero-title {
    font-size: 24px;
  }

  .price-card,
  .description-card,
  .features-card,
  .order-card,
  .image-gallery {
    padding: 16px;
    border-radius: 12px;
  }

  .current-price {
    font-size: 24px;
  }

  .card-title,
  .order-title {
    font-size: 18px;
  }

  .feature-item {
    padding: 10px;
  }

  .gallery-main-image {
    height: 180px;
  }

  .thumbnail-image {
    width: 45px;
    height: 45px;
  }

  .add-to-cart-primary {
    padding: 14px 20px;
    font-size: 15px;
  }

  .secondary-actions {
    flex-direction: column;
  }

  .action-btn {
    padding: 10px 14px;
  }
}
