import { Link } from "react-router-dom";
import { assets } from "../../assets/assets";
import "./Footer.css";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="modern-footer">
      {/* Main Footer Content */}
      <div className="footer-main">
        <div className="footer-container">
          {/* Brand Section */}
          <div className="footer-brand">
            <div className="brand-logo">
              <img src={assets.logo} alt="Tomato Logo" className="footer-logo" />
              <div className="brand-info">
                <h3 className="brand-name">Tomato</h3>
                <p className="brand-tagline">Delicious Food Delivered</p>
              </div>
            </div>
            <p className="brand-description">
              Experience the finest flavors delivered fresh to your doorstep.
              From authentic cuisines to modern delights, we bring the world's
              best food right to you.
            </p>
            <div className="social-links">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="social-link facebook">
                <span className="social-icon">📘</span>
                <span className="social-text">Facebook</span>
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="social-link instagram">
                <span className="social-icon">📷</span>
                <span className="social-text">Instagram</span>
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="social-link twitter">
                <span className="social-icon">🐦</span>
                <span className="social-text">Twitter</span>
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="social-link linkedin">
                <span className="social-icon">💼</span>
                <span className="social-text">LinkedIn</span>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h4 className="section-title">Quick Links</h4>
            <ul className="footer-links">
              <li>
                <Link to="/" className="footer-link">
                  <span className="link-icon">🏠</span>
                  <span className="link-text">Home</span>
                </Link>
              </li>
              <li>
                <Link to="/menu" className="footer-link">
                  <span className="link-icon">🍽️</span>
                  <span className="link-text">Menu</span>
                </Link>
              </li>
              <li>
                <Link to="/about" className="footer-link">
                  <span className="link-icon">ℹ️</span>
                  <span className="link-text">About Us</span>
                </Link>
              </li>
              <li>
                <Link to="/contact" className="footer-link">
                  <span className="link-icon">📞</span>
                  <span className="link-text">Contact Us</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="footer-section">
            <h4 className="section-title">Our Services</h4>
            <ul className="footer-links">
              <li>
                <Link to="/delivery" className="footer-link">
                  <span className="link-icon">🚚</span>
                  <span className="link-text">Fast Delivery</span>
                </Link>
              </li>
              <li>
                <Link to="/catering" className="footer-link">
                  <span className="link-icon">🎉</span>
                  <span className="link-text">Catering</span>
                </Link>
              </li>
              <li>
                <Link to="/reservations" className="footer-link">
                  <span className="link-icon">📅</span>
                  <span className="link-text">Reservations</span>
                </Link>
              </li>
              <li>
                <Link to="/gift-cards" className="footer-link">
                  <span className="link-icon">🎁</span>
                  <span className="link-text">Gift Cards</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h4 className="section-title">Get in Touch</h4>
            <div className="contact-info">
              <div className="contact-item">
                <span className="contact-icon">📍</span>
                <div className="contact-details">
                  <span className="contact-label">Address</span>
                  <span className="contact-value">123 Food Street, Culinary City, FC 12345</span>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📞</span>
                <div className="contact-details">
                  <span className="contact-label">Phone</span>
                  <span className="contact-value">+****************</span>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">📧</span>
                <div className="contact-details">
                  <span className="contact-label">Email</span>
                  <span className="contact-value"><EMAIL></span>
                </div>
              </div>
              <div className="contact-item">
                <span className="contact-icon">⏰</span>
                <div className="contact-details">
                  <span className="contact-label">Hours</span>
                  <span className="contact-value">Mon-Sun: 9:00 AM - 11:00 PM</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <div className="footer-newsletter">
        <div className="newsletter-container">
          <div className="newsletter-content">
            <div className="newsletter-info">
              <h4 className="newsletter-title">Stay Updated</h4>
              <p className="newsletter-description">
                Subscribe to get special offers, free giveaways, and exclusive deals.
              </p>
            </div>
            <div className="newsletter-form">
              <div className="email-input-group">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="email-input"
                />
                <button className="subscribe-btn">
                  <span className="btn-text">Subscribe</span>
                  <span className="btn-icon">📧</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Bottom */}
      <div className="footer-bottom">
        <div className="footer-bottom-container">
          <div className="copyright-section">
            <p className="copyright-text">
              © {currentYear} Tomato. All rights reserved.
            </p>
            <p className="company-info">
              Made with ❤️ for food lovers everywhere
            </p>
          </div>
          <div className="legal-links">
            <Link to="/privacy" className="legal-link">Privacy Policy</Link>
            <Link to="/terms" className="legal-link">Terms of Service</Link>
            <Link to="/cookies" className="legal-link">Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
