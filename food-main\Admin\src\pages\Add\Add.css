/* General Styles */
.add {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 20px auto;
    width: 100%;
}

/* Flexbox Styles */
.flex-col {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Align items to the left */
}

/* Upload Image Section */
.add-img-upload {
    margin-bottom: 20px;
}

/* Upload Image Styles */
.add-img-upload label {
    cursor: pointer; /* Pointer cursor for upload area */
}

.add-img-upload img {
    width: 100%; /* Responsive image */
    max-width: 200px; /* Limit image size */
    border: 1px dashed #ddd; /* Dashed border for upload area */
    border-radius: 4px; /* Rounded corners */
    margin-bottom: 10px; /* Space below image */
}

/* Input Fields */
input[type="text"],
textarea,
select {
    width: 100%; /* Full width for inputs */
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px; /* Consistent font size */
    margin-bottom: 15px; /* Space below each input */
    transition: border-color 0.3s; /* Transition for focus effect */
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    border-color: #007bff; /* Change border color on focus */
    outline: none; /* Remove default outline */
}

/* Category and Price Section */
.add-category-price {
    display: flex;
    gap: 20px;
    width: 100%;
}

.add-category,
.add-price {
    flex: 1;
}

.add-category p,
.add-price p {
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

/* Button Styles */
.add-product-button {
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 20px;
    min-height: 44px;
}

.add-product-button:hover {
    background-color: #0056b3;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .add {
        padding: 15px;
        margin: 10px;
        border-radius: 6px;
    }

    .add-img-upload img {
        max-width: 120px;
    }

    .add-category-price {
        flex-direction: column;
        gap: 15px;
    }

    input[type="text"],
    input[type="number"],
    textarea,
    select {
        font-size: 16px;
        padding: 12px;
        min-height: 44px;
    }

    .add-product-button {
        width: 100%;
        padding: 14px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .add {
        padding: 12px;
        margin: 5px;
    }

    .add-img-upload img {
        max-width: 100px;
    }

    input[type="text"],
    input[type="number"],
    textarea,
    select {
        padding: 10px;
        font-size: 15px;
    }

    .add-product-button {
        padding: 12px;
        font-size: 15px;
    }
}
