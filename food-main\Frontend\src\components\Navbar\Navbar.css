/* General Navbar Styles */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: white;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
}

.logo {
    width: 150px;
    height: auto;
    max-width: 100%;
}

.navbar-hamburger {
    display: none; /* Hidden by default */
    font-size: 24px;
    cursor: pointer;
    color: #49557e; /* Default color */
    transition: color 0.3s; /* Smooth color transition */
}

.navbar-hamburger.open {
    color: tomato; /* Change color when menu is open */
}

.navbar-menu {
    display: flex;
    list-style: none;
    gap: 20px;
    color: #49557e;
    font-size: 18px;
}

.navbar-menu.open {
    display: flex; /* Show menu when toggled */
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 40px;
}

.navbar button {
    background: transparent;
    font-size: 16px;
    color: #49557e;
    border: 1px solid tomato;
    padding: 10px 30px;
    border-radius: 50px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.navbar button:hover {
    background-color: rgb(174, 133, 133);
}

.navbar .active {
    padding-bottom: 2px;
    border-bottom: 2px solid #49557e;
}

.navbar li {
    cursor: pointer;
}

.navbar-search-icon {
    position: relative;
}

.navbar-search-icon .dot {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: tomato;
    top: -8px;
    right: -8px;
    border-radius: 50%;
}

/* Profile Dropdown Menu Styles */
.navbar-profile {
    position: relative;
}

.nav-profile-dropdown {
    display: none; /* Hidden by default */
    position: absolute;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    list-style: none;
    padding: 10px 0;
    z-index: 10;
}

.navbar-profile:hover .nav-profile-dropdown {
    display: block; /* Show dropdown on hover */
}

.nav-profile-dropdown li {
    padding: 10px 15px; /* Add padding for dropdown items */
    display: flex;
    align-items: center; /* Center icons and text */
    cursor: pointer;
    transition: background-color 0.2s; /* Smooth background transition */
}

.nav-profile-dropdown li:hover {
    background-color: #f5f5f5; /* Background color on hover */
}

/* Larger Icons for Profile, Orders, Logout */
.navbar-profile img,
.nav-profile-dropdown li img {
    width: 24px; /* Adjust size */
    height: 24px; /* Adjust size */
    margin-right: 10px; /* Spacing between icon and text */
}

.nav-profile-dropdown li p {
    margin: 0; /* Remove default margin from paragraph */
    color: #49557e; /* Color for the text */
}
.nav-profile-dropdown {
    display: flex;
    flex-direction: column;
    position: absolute; /* Position it absolutely to the profile icon */
    background-color: rgb(223, 204, 204);
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    list-style: none;
    padding: 10px 0;
    z-index: 10; /* Ensure it is above other elements */
    margin-top: 10px; /* Spacing below the profile icon */
}

.nav-profile-dropdown li {
    padding: 10px 15px; /* Spacing for dropdown items */
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s; /* Smooth background transition */
}

.nav-profile-dropdown li:hover {
    background-color: #f5f5f5; /* Change background color on hover */
}

/* Responsive Styling */
@media (max-width: 768px) {
    .navbar {
        padding: 10px 15px;
        position: sticky;
        top: 0;
    }

    .navbar-hamburger {
        display: block;
        font-size: 28px;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .navbar-hamburger:hover {
        background-color: #f5f5f5;
    }

    .navbar-menu {
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: white;
        width: 100%;
        padding: 20px;
        display: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border-top: 1px solid #eee;
    }

    .navbar-menu.open {
        display: flex;
        animation: slideDown 0.3s ease-out;
    }

    .navbar-menu li {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 16px;
    }

    .navbar-menu li:last-child {
        border-bottom: none;
    }

    .navbar-right {
        gap: 15px;
    }

    .navbar-search-icon {
        display: none;
    }

    .navbar-basket-icon .dot {
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
        background-color: tomato;
        color: white;
        min-width: 18px;
        text-align: center;
    }

    .logo {
        width: 120px;
    }

    .navbar button {
        padding: 10px 20px;
        font-size: 14px;
        min-height: 44px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 8px 10px;
    }

    .navbar-right {
        gap: 10px;
    }

    .navbar button {
        padding: 8px 16px;
        font-size: 13px;
        min-height: 40px;
    }

    .logo {
        width: 100px;
    }

    .navbar-basket-icon img,
    .navbar-profile img {
        width: 20px;
        height: 20px;
    }

    .nav-profile-dropdown {
        right: 0;
        left: auto;
        min-width: 150px;
    }
}

/* Animation for mobile menu */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Touch-friendly improvements */
@media (max-width: 768px) {
    .navbar-menu li a,
    .navbar-menu li {
        display: block;
        padding: 15px 0;
        touch-action: manipulation;
    }

    .navbar-basket-icon,
    .navbar-profile {
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .navbar-basket-icon:hover,
    .navbar-profile:hover {
        background-color: #f5f5f5;
    }
}
