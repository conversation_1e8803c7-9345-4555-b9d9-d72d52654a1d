/* Header Container */
.header {
    height: 80vh;
    margin: 0;
    position: relative;
    overflow: hidden;
    border-radius: 0 0 30px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Carousel Container */
.header-carousel {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Individual Slides */
.header-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    overflow: hidden;
}

.header-slide.active {
    opacity: 1;
}

/* Slide Background for Images */
.slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform 0.8s ease;
}

.header-slide.active .slide-background {
    transform: scale(1.05);
}

/* Video Slides */
.slide-video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
    z-index: 1;
}

/* Media Type Indicator */
.media-type-indicator {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 16px;
    backdrop-filter: blur(10px);
    z-index: 15;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Slide Overlay */
.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.6) 0%,
        rgba(0, 0, 0, 0.2) 50%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 5;
}

/* Header Contents */
.header-contents {
    position: absolute;
    top: 50%;
    left: 6vw;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    max-width: 50%;
    z-index: 10;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.header-contents h2 {
    font-weight: 700;
    color: white;
    font-size: max(4.5vw, 32px);
    line-height: 1.2;
    margin: 0;
    animation: slideInLeft 1s ease-out;
}

.header-contents p {
    font-size: max(1.2vw, 16px);
    color: rgba(255,255,255,0.95);
    line-height: 1.6;
    margin: 0;
    animation: slideInLeft 1s ease-out 0.3s both;
}

/* Header Buttons */
.header-buttons {
    display: flex;
    gap: 15px;
    animation: slideInLeft 1s ease-out 0.6s both;
}

.view-menu-btn,
.order-now-btn {
    border: none;
    padding: 15px 30px;
    border-radius: 30px;
    font-size: max(1vw, 16px);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.view-menu-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.view-menu-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.order-now-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255,107,107,0.4);
}

.order-now-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255,107,107,0.5);
}

/* Slide Info */
.slide-info {
    position: absolute;
    bottom: 120px;
    left: 6vw;
    z-index: 15;
    animation: fadeInUp 1s ease-out 0.9s both;
    background: rgba(0, 0, 0, 0.4);
    padding: 20px 25px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 400px;
}

.slide-counter {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.slide-info h3 {
    color: white;
    font-size: max(2vw, 22px);
    font-weight: 700;
    margin: 0 0 8px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
    line-height: 1.2;
}

.slide-info p {
    color: rgba(255,255,255,0.95);
    font-size: max(1vw, 15px);
    margin: 0 0 12px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    line-height: 1.4;
}

.media-type-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Navigation Arrows */
.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.carousel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* Dots Indicator */
.carousel-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 15;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 15px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.dot {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.dot-icon {
    font-size: 12px;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.dot.active {
    background: rgba(255, 255, 255, 0.9);
    border-color: white;
    transform: scale(1.1);
}

.dot.active .dot-icon {
    opacity: 1;
    font-size: 14px;
}

.dot:hover {
    border-color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.dot:hover .dot-icon {
    opacity: 1;
}

/* Different styles for video dots */
.dot.video {
    border-color: rgba(255, 107, 107, 0.7);
}

.dot.video.active {
    background: rgba(255, 107, 107, 0.9);
    border-color: #ff6b6b;
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .header {
        height: 60vh;
        border-radius: 0 0 20px 20px;
    }

    .header-contents {
        left: 4vw;
        max-width: 75%;
        gap: 15px;
    }

    .header-contents h2 {
        font-size: max(6vw, 24px);
    }

    .header-contents p {
        font-size: max(2vw, 16px);
    }

    .header-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 12px 24px;
        font-size: 14px;
        width: 100%;
        max-width: 200px;
    }

    .slide-info {
        bottom: 100px;
        left: 4vw;
        max-width: 300px;
        padding: 15px 20px;
    }

    .slide-info h3 {
        font-size: max(4vw, 18px);
    }

    .slide-info p {
        font-size: max(2.5vw, 14px);
    }

    .media-type-indicator {
        top: 15px;
        left: 15px;
        padding: 6px 10px;
        font-size: 14px;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 24px;
    }

    .prev-btn {
        left: 15px;
    }

    .next-btn {
        right: 15px;
    }

    .carousel-dots {
        bottom: 20px;
        gap: 6px;
        padding: 8px 12px;
    }

    .dot {
        width: 30px;
        height: 30px;
    }

    .dot-icon {
        font-size: 10px;
    }

    .dot.active .dot-icon {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .header {
        height: 50vh;
        border-radius: 0 0 15px 15px;
    }

    .header-contents {
        left: 5%;
        max-width: 85%;
        gap: 12px;
    }

    .header-contents h2 {
        font-size: max(7vw, 22px);
        line-height: 1.1;
    }

    .header-contents p {
        font-size: max(3.5vw, 15px);
        line-height: 1.4;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 10px 20px;
        font-size: 13px;
        max-width: 180px;
    }

    .slide-info {
        bottom: 80px;
        left: 5%;
        max-width: 250px;
        padding: 12px 16px;
    }

    .slide-counter {
        font-size: 10px;
        margin-bottom: 6px;
    }

    .slide-info h3 {
        font-size: max(5vw, 16px);
    }

    .slide-info p {
        font-size: max(3vw, 12px);
        margin-bottom: 8px;
    }

    .media-type-badge {
        font-size: 9px;
        padding: 4px 8px;
    }

    .media-type-indicator {
        top: 10px;
        left: 10px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .carousel-btn {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .prev-btn {
        left: 10px;
    }

    .next-btn {
        right: 10px;
    }

    .carousel-dots {
        bottom: 15px;
        gap: 4px;
        padding: 6px 10px;
    }

    .dot {
        width: 25px;
        height: 25px;
    }

    .dot-icon {
        font-size: 8px;
    }

    .dot.active .dot-icon {
        font-size: 10px;
    }
}

@media (max-width: 360px) {
    .header {
        height: 45vh;
    }

    .header-contents {
        left: 4%;
        max-width: 90%;
        gap: 10px;
    }

    .header-contents h2 {
        font-size: 20px;
    }

    .header-contents p {
        font-size: 14px;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 8px 16px;
        font-size: 12px;
        max-width: 160px;
    }

    .slide-info {
        bottom: 50px;
        left: 4%;
    }

    .slide-info h3 {
        font-size: 15px;
    }

    .slide-info p {
        font-size: 11px;
    }
}
