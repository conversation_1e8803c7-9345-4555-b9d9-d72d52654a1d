.header {
    height: 34vw;
    min-height: 300px;
    max-height: 500px;
    margin: 20px auto;
    background: url(https://i.pinimg.com/736x/6b/11/e8/6b11e8e4e528c04c5893f4f1d06255ec.jpg) no-repeat center center;
    background-size: cover;
    background-attachment: fixed;
    position: relative;
    color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
    z-index: 1;
}

.header-contents {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5vw;
    max-width: 50%;
    bottom: 10%;
    left: 6vw;
    z-index: 2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.header-contents h2 {
    font-weight: 600;
    color: white;
    font-size: max(4.5vw, 24px);
    line-height: 1.2;
    margin-bottom: 10px;
}

.header-contents p {
    font-size: max(1.2vw, 14px);
    color: rgba(255,255,255,0.9);
    line-height: 1.4;
    margin-bottom: 20px;
}

.header-contents button {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: max(1vw, 14px);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,107,107,0.3);
}

.header-contents button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255,107,107,0.4);
}

@media (max-width: 768px) {
    .header {
        height: 45vw;
        min-height: 250px;
        max-height: 350px;
        margin: 15px auto;
        background-attachment: scroll;
        border-radius: 8px;
    }

    .header-contents {
        left: 4vw;
        bottom: 15%;
        max-width: 75%;
        gap: 2vw;
    }

    .header-contents h2 {
        font-size: max(6vw, 22px);
        line-height: 1.1;
    }

    .header-contents p {
        font-size: max(1.8vw, 16px);
        margin-bottom: 15px;
    }

    .header-contents button {
        padding: 14px 28px;
        font-size: max(1.5vw, 16px);
        min-height: 44px;
    }
}

@media (max-width: 480px) {
    .header {
        height: 50vw;
        min-height: 200px;
        max-height: 280px;
        margin: 10px auto;
        border-radius: 6px;
    }

    .header-contents {
        left: 5%;
        bottom: 10%;
        max-width: 85%;
        gap: 3vw;
        text-align: left;
    }

    .header-contents h2 {
        font-size: max(7vw, 20px);
        line-height: 1.1;
        margin-bottom: 8px;
    }

    .header-contents p {
        font-size: max(3vw, 14px);
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .header-contents button {
        padding: 12px 24px;
        font-size: 14px;
        min-height: 44px;
        width: auto;
        align-self: flex-start;
    }
}

@media (max-width: 360px) {
    .header {
        height: 55vw;
        min-height: 180px;
        margin: 8px auto;
    }

    .header-contents {
        left: 4%;
        bottom: 8%;
        max-width: 90%;
    }

    .header-contents h2 {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .header-contents p {
        font-size: 13px;
        margin-bottom: 10px;
    }

    .header-contents button {
        padding: 10px 20px;
        font-size: 13px;
        min-height: 40px;
    }
}
