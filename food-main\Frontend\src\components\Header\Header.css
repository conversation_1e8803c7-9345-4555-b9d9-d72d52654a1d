/* Header Container */
.header {
    height: 80vh;
    margin: 0;
    position: relative;
    overflow: hidden;
    border-radius: 0 0 30px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Carousel Container */
.header-carousel {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Individual Slides */
.header-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.header-slide.active {
    opacity: 1;
}

/* Slide Overlay */
.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(0, 0, 0, 0.3) 50%,
        rgba(0, 0, 0, 0.5) 100%
    );
}

/* Header Contents */
.header-contents {
    position: absolute;
    top: 50%;
    left: 6vw;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    max-width: 50%;
    z-index: 10;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.header-contents h2 {
    font-weight: 700;
    color: white;
    font-size: max(4.5vw, 32px);
    line-height: 1.2;
    margin: 0;
    animation: slideInLeft 1s ease-out;
}

.header-contents p {
    font-size: max(1.2vw, 16px);
    color: rgba(255,255,255,0.95);
    line-height: 1.6;
    margin: 0;
    animation: slideInLeft 1s ease-out 0.3s both;
}

/* Header Buttons */
.header-buttons {
    display: flex;
    gap: 15px;
    animation: slideInLeft 1s ease-out 0.6s both;
}

.view-menu-btn,
.order-now-btn {
    border: none;
    padding: 15px 30px;
    border-radius: 30px;
    font-size: max(1vw, 16px);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.view-menu-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.view-menu-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.order-now-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255,107,107,0.4);
}

.order-now-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255,107,107,0.5);
}

/* Slide Info */
.slide-info {
    position: absolute;
    bottom: 100px;
    left: 6vw;
    z-index: 10;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.slide-info h3 {
    color: white;
    font-size: max(2vw, 20px);
    font-weight: 600;
    margin: 0 0 5px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.slide-info p {
    color: rgba(255,255,255,0.9);
    font-size: max(1vw, 14px);
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

/* Navigation Arrows */
.carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.carousel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* Dots Indicator */
.carousel-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: white;
    border-color: white;
    transform: scale(1.2);
}

.dot:hover {
    border-color: white;
    transform: scale(1.1);
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .header {
        height: 60vh;
        border-radius: 0 0 20px 20px;
    }

    .header-contents {
        left: 4vw;
        max-width: 75%;
        gap: 15px;
    }

    .header-contents h2 {
        font-size: max(6vw, 24px);
    }

    .header-contents p {
        font-size: max(2vw, 16px);
    }

    .header-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 12px 24px;
        font-size: 14px;
        width: 100%;
        max-width: 200px;
    }

    .slide-info {
        bottom: 80px;
        left: 4vw;
    }

    .slide-info h3 {
        font-size: max(4vw, 18px);
    }

    .slide-info p {
        font-size: max(2.5vw, 14px);
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 24px;
    }

    .prev-btn {
        left: 15px;
    }

    .next-btn {
        right: 15px;
    }

    .carousel-dots {
        bottom: 20px;
        gap: 8px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .header {
        height: 50vh;
        border-radius: 0 0 15px 15px;
    }

    .header-contents {
        left: 5%;
        max-width: 85%;
        gap: 12px;
    }

    .header-contents h2 {
        font-size: max(7vw, 22px);
        line-height: 1.1;
    }

    .header-contents p {
        font-size: max(3.5vw, 15px);
        line-height: 1.4;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 10px 20px;
        font-size: 13px;
        max-width: 180px;
    }

    .slide-info {
        bottom: 60px;
        left: 5%;
    }

    .slide-info h3 {
        font-size: max(5vw, 16px);
    }

    .slide-info p {
        font-size: max(3vw, 12px);
    }

    .carousel-btn {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .prev-btn {
        left: 10px;
    }

    .next-btn {
        right: 10px;
    }

    .carousel-dots {
        bottom: 15px;
        gap: 6px;
    }

    .dot {
        width: 8px;
        height: 8px;
    }
}

@media (max-width: 360px) {
    .header {
        height: 45vh;
    }

    .header-contents {
        left: 4%;
        max-width: 90%;
        gap: 10px;
    }

    .header-contents h2 {
        font-size: 20px;
    }

    .header-contents p {
        font-size: 14px;
    }

    .view-menu-btn,
    .order-now-btn {
        padding: 8px 16px;
        font-size: 12px;
        max-width: 160px;
    }

    .slide-info {
        bottom: 50px;
        left: 4%;
    }

    .slide-info h3 {
        font-size: 15px;
    }

    .slide-info p {
        font-size: 11px;
    }
}
