import { useContext, useState } from 'react';
import { menu_list } from '../../assets/assets'; // Ensure menu_list is an array of objects
import { StoreContext } from '../../context/StoreContext';
import './ExploreMenu.css';

const ExploreMenu = ({ category, setCategory }) => {
  const { food_list, addToCart } = useContext(StoreContext);
  const [showFoodItems, setShowFoodItems] = useState(false);
  const [orderNotification, setOrderNotification] = useState(null);

  // Get featured food items (limit to 6 for preview)
  const featuredItems = food_list ? food_list.slice(0, 6) : [];

  const handleOrderNow = (foodId, foodName) => {
    addToCart(foodId);

    // Show notification
    setOrderNotification(`${foodName} added to cart! 🛒`);

    // Hide notification after 3 seconds
    setTimeout(() => {
      setOrderNotification(null);
    }, 3000);

    console.log(`Added item ${foodId} to cart`);
  };

  return (
    <div className='explore-menu' id='explore-menu'>
      <h1>Explore Our Menu</h1>
      <p className='explore-menu-text'>
        Choose from a diverse menu featuring a delectable array of dishes
      </p>

      {/* Category Selection */}
      <div className='explore-menu-list'>
        {menu_list.map((item, index) => (
          <div
            onClick={() => setCategory(prev => (prev === item.menu_name ? "All" : item.menu_name))}
            key={index}
            className='explore-menu-list-item'
          >
            <img
              className={category === item.menu_name ? "active" : ""}
              src={item.menu_image}
              alt={item.menu_name}
            />
            <p>{item.menu_name}</p>
          </div>
        ))}
      </div>

      {/* Featured Food Items Section */}
      <div className='featured-items-section'>
        <div className='featured-header'>
          <h2>🌟 Featured Items</h2>
          <p>Try our most popular dishes</p>
          <button
            className='toggle-view-btn'
            onClick={() => setShowFoodItems(!showFoodItems)}
          >
            {showFoodItems ? 'Hide Items' : 'View All Items'}
            <span className='toggle-icon'>{showFoodItems ? '▲' : '▼'}</span>
          </button>
        </div>

        {showFoodItems && (
          <div className='featured-items-grid'>
            {featuredItems.length > 0 ? (
              featuredItems.map((item) => (
                <div key={item._id} className='featured-food-card'>
                  {/* Food Type Indicator */}
                  <div className={`food-type-badge ${(item.foodType || 'Vegetarian') === 'Vegetarian' ? 'veg' : 'non-veg'}`}>
                    <span className='food-type-icon'>
                      {(item.foodType || 'Vegetarian') === 'Vegetarian' ? '🌱' : '🍖'}
                    </span>
                    <span className='food-type-text'>
                      {(item.foodType || 'Vegetarian') === 'Vegetarian' ? 'VEG' : 'NON-VEG'}
                    </span>
                  </div>

                  {/* Food Image */}
                  <div className='featured-food-image'>
                    <img
                      src={`http://localhost:8889/uploads/${item.image}`}
                      alt={item.name}
                      onError={(e) => {
                        e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80";
                      }}
                    />
                    <div className='image-overlay'>
                      <span className='category-tag'>{item.category}</span>
                    </div>
                  </div>

                  {/* Food Info */}
                  <div className='featured-food-info'>
                    <h3 className='food-name'>{item.name}</h3>
                    <p className='food-description'>{item.description}</p>
                    <div className='food-price'>₹{item.price}</div>
                  </div>

                  {/* Order Button */}
                  <div className='featured-food-actions'>
                    <button
                      className='order-now-btn'
                      onClick={() => handleOrderNow(item._id, item.name)}
                    >
                      <span className='btn-icon'>🛒</span>
                      Order Now
                    </button>
                    <div className='quick-actions'>
                      <button className='quick-btn favorite'>❤️</button>
                      <button className='quick-btn share'>📤</button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className='no-items-message'>
                <div className='no-items-icon'>🍽️</div>
                <h3>No items available</h3>
                <p>Check back later for delicious food items!</p>
              </div>
            )}
          </div>
        )}
      </div>

      <hr />
    </div>
  );
};

export default ExploreMenu;
