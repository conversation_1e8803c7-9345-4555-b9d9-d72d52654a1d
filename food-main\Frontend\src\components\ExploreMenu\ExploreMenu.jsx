import { useContext, useState } from 'react';
import { menu_list } from '../../assets/assets'; // Ensure menu_list is an array of objects
import { StoreContext } from '../../context/StoreContext';
import './ExploreMenu.css';

const ExploreMenu = ({ category, setCategory }) => {
  const { food_list, addToCart } = useContext(StoreContext);
  const [showFoodItems, setShowFoodItems] = useState(false);
  const [orderNotification, setOrderNotification] = useState(null);
  const [foodTypeFilter, setFoodTypeFilter] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  // Filter food items based on food type and search
  const getFilteredItems = () => {
    if (!food_list) return [];

    let filtered = food_list;

    // Filter by food type
    if (foodTypeFilter !== 'All') {
      filtered = filtered.filter(item => {
        const itemFoodType = item.foodType || 'Vegetarian';
        return itemFoodType === foodTypeFilter;
      });
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered.slice(0, 6); // Limit to 6 items
  };

  const featuredItems = getFilteredItems();

  const handleOrderNow = (foodId, foodName) => {
    addToCart(foodId);

    // Show notification
    setOrderNotification(`${foodName} added to cart! 🛒`);

    // Hide notification after 3 seconds
    setTimeout(() => {
      setOrderNotification(null);
    }, 3000);

    console.log(`Added item ${foodId} to cart`);
  };

  // Get counts for each filter
  const getCounts = () => {
    if (!food_list) return { all: 0, veg: 0, nonVeg: 0 };

    const all = food_list.length;
    const veg = food_list.filter(item => (item.foodType || 'Vegetarian') === 'Vegetarian').length;
    const nonVeg = food_list.filter(item => (item.foodType || 'Vegetarian') === 'Non-Vegetarian').length;

    return { all, veg, nonVeg };
  };

  const counts = getCounts();

  return (
    <div className='explore-menu' id='explore-menu'>
      {/* Order Notification */}
      {orderNotification && (
        <div className='order-notification'>
          {orderNotification}
        </div>
      )}

      {/* Modern Header Section */}
      <div className='explore-header'>
        <div className='header-content'>
          <div className='header-badge'>
            <span className='badge-icon'>🍽️</span>
            <span className='badge-text'>Menu Categories</span>
          </div>
          <h1 className='header-title'>Explore Our Delicious Menu</h1>
          <p className='header-subtitle'>
            Discover amazing flavors from our carefully curated collection of dishes,
            made with fresh ingredients and crafted with passion
          </p>
          <div className='header-stats'>
            <div className='stat-card'>
              <span className='stat-icon'>🍕</span>
              <div className='stat-info'>
                <span className='stat-number'>{counts.all}</span>
                <span className='stat-label'>Total Dishes</span>
              </div>
            </div>
            <div className='stat-card'>
              <span className='stat-icon'>🌱</span>
              <div className='stat-info'>
                <span className='stat-number'>{counts.veg}</span>
                <span className='stat-label'>Vegetarian</span>
              </div>
            </div>
            <div className='stat-card'>
              <span className='stat-icon'>⭐</span>
              <div className='stat-info'>
                <span className='stat-number'>4.8</span>
                <span className='stat-label'>Rating</span>
              </div>
            </div>
          </div>
        </div>
        <div className='header-visual'>
          <div className='floating-elements'>
            <div className='floating-item item-1'>🍕</div>
            <div className='floating-item item-2'>🍔</div>
            <div className='floating-item item-3'>🍜</div>
            <div className='floating-item item-4'>🥗</div>
            <div className='floating-item item-5'>🍰</div>
          </div>
        </div>
      </div>

      {/* Modern Category Selection */}
      <div className='categories-section'>
        <div className='section-header'>
          <h2 className='section-title'>Browse by Category</h2>
          <p className='section-subtitle'>Choose your favorite food category</p>
        </div>

        <div className='categories-grid'>
          {menu_list.map((item, index) => (
            <div
              onClick={() => setCategory(prev => (prev === item.menu_name ? "All" : item.menu_name))}
              key={index}
              className={`category-card ${category === item.menu_name ? "active" : ""}`}
            >
              <div className='category-image-container'>
                <img
                  src={item.menu_image}
                  alt={item.menu_name}
                  className='category-image'
                />
                <div className='category-overlay'>
                  <span className='category-icon'>✨</span>
                </div>
              </div>
              <div className='category-info'>
                <h3 className='category-name'>{item.menu_name}</h3>
                <p className='category-description'>Fresh & Delicious</p>
                <div className='category-badge'>
                  <span className='badge-dot'></span>
                  <span className='badge-text'>Popular</span>
                </div>
              </div>
              <div className='category-arrow'>→</div>
            </div>
          ))}
        </div>
      </div>

      {/* Modern Filter Section */}
      <div className='modern-filter-section'>
        <div className='filter-header'>
          <h3 className='filter-title'>Quick Filters</h3>
          <p className='filter-subtitle'>Find exactly what you're craving</p>
        </div>

        <div className='filter-controls'>
          <div className='filter-pills'>
            <button
              className={`filter-pill ${foodTypeFilter === 'All' ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter('All')}
            >
              <span className='pill-icon'>🍽️</span>
              <span className='pill-text'>All Items</span>
              <span className='pill-count'>{counts.all}</span>
            </button>

            <button
              className={`filter-pill veg-pill ${foodTypeFilter === 'Vegetarian' ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter('Vegetarian')}
            >
              <span className='pill-icon'>🌱</span>
              <span className='pill-text'>Vegetarian</span>
              <span className='pill-count'>{counts.veg}</span>
            </button>

            <button
              className={`filter-pill non-veg-pill ${foodTypeFilter === 'Non-Vegetarian' ? 'active' : ''}`}
              onClick={() => setFoodTypeFilter('Non-Vegetarian')}
            >
              <span className='pill-icon'>🍖</span>
              <span className='pill-text'>Non-Vegetarian</span>
              <span className='pill-count'>{counts.nonVeg}</span>
            </button>
          </div>

          <div className='modern-search'>
            <div className='search-box'>
              <div className='search-icon-container'>
                <span className='search-icon'>🔍</span>
              </div>
              <input
                type='text'
                placeholder='Search dishes, ingredients, or cuisine...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='modern-search-input'
              />
              {searchTerm && (
                <button
                  className='clear-search-btn'
                  onClick={() => setSearchTerm('')}
                >
                  <span>✕</span>
                </button>
              )}
              <div className='search-suggestions'>
                <span className='suggestion-tag'>Pizza</span>
                <span className='suggestion-tag'>Burger</span>
                <span className='suggestion-tag'>Salad</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Featured Items Section */}
      <div className='modern-featured-section'>
        <div className='featured-section-header'>
          <div className='header-left'>
            <div className='featured-badge'>
              <span className='badge-icon'>⭐</span>
              <span className='badge-text'>Featured</span>
            </div>
            <h2 className='featured-title'>Chef's Special Recommendations</h2>
            <p className='featured-description'>
              Handpicked favorites that our customers love most. Each dish is crafted
              with premium ingredients and authentic flavors.
            </p>
          </div>
          <div className='header-right'>
            <button
              className='modern-toggle-btn'
              onClick={() => setShowFoodItems(!showFoodItems)}
            >
              <span className='toggle-text'>
                {showFoodItems ? 'Hide Collection' : 'Explore Collection'}
              </span>
              <span className='toggle-arrow'>
                {showFoodItems ? '↑' : '↓'}
              </span>
            </button>
          </div>
        </div>

        {showFoodItems && (
          <div className='modern-food-grid'>
            {featuredItems.length > 0 ? (
              featuredItems.map((item) => (
                <div key={item._id} className='modern-food-card'>
                  {/* Modern Food Type Badge */}
                  <div className={`modern-food-badge ${(item.foodType || 'Vegetarian') === 'Vegetarian' ? 'veg-badge' : 'non-veg-badge'}`}>
                    <div className='badge-content'>
                      <span className='badge-icon'>
                        {(item.foodType || 'Vegetarian') === 'Vegetarian' ? '🌱' : '🍖'}
                      </span>
                      <span className='badge-label'>
                        {(item.foodType || 'Vegetarian') === 'Vegetarian' ? 'VEG' : 'NON-VEG'}
                      </span>
                    </div>
                  </div>

                  {/* Rating Badge */}
                  <div className='rating-badge'>
                    <span className='rating-star'>⭐</span>
                    <span className='rating-value'>4.{Math.floor(Math.random() * 5) + 5}</span>
                  </div>

                  {/* Modern Food Image */}
                  <div className='modern-food-image'>
                    <div className='image-container'>
                      <img
                        src={`http://localhost:8889/uploads/${item.image}`}
                        alt={item.name}
                        onError={(e) => {
                          e.target.src = "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80";
                        }}
                        className='food-image'
                      />
                      <div className='image-gradient'></div>
                      <div className='image-actions'>
                        <button className='quick-view-btn'>👁️</button>
                        <button className='favorite-btn'>❤️</button>
                      </div>
                    </div>
                    <div className='category-ribbon'>
                      <span className='ribbon-text'>{item.category}</span>
                    </div>
                  </div>

                  {/* Modern Food Info */}
                  <div className='modern-food-info'>
                    <div className='food-header'>
                      <h3 className='modern-food-name'>{item.name}</h3>
                      <div className='food-meta'>
                        <span className='prep-time'>⏱️ 15-20 min</span>
                        <span className='serving-size'>👥 Serves 1-2</span>
                      </div>
                    </div>
                    <p className='modern-food-description'>{item.description}</p>
                    <div className='price-section'>
                      <div className='price-info'>
                        <span className='current-price'>₹{item.price}</span>
                        <span className='original-price'>₹{Math.floor(item.price * 1.2)}</span>
                        <span className='discount-badge'>
                          {Math.floor(((Math.floor(item.price * 1.2) - item.price) / Math.floor(item.price * 1.2)) * 100)}% OFF
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Modern Order Actions */}
                  <div className='modern-food-actions'>
                    <div className='quantity-selector'>
                      <button className='qty-btn minus'>−</button>
                      <span className='qty-display'>1</span>
                      <button className='qty-btn plus'>+</button>
                    </div>
                    <button
                      className='modern-order-btn'
                      onClick={() => handleOrderNow(item._id, item.name)}
                    >
                      <span className='order-icon'>🛒</span>
                      <span className='order-text'>Add to Cart</span>
                      <span className='order-price'>₹{item.price}</span>
                    </button>
                    <div className='action-buttons'>
                      <button className='action-btn share-btn' title='Share'>
                        <span>📤</span>
                      </button>
                      <button className='action-btn compare-btn' title='Compare'>
                        <span>⚖️</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className='modern-no-items'>
                <div className='no-items-visual'>
                  <div className='empty-plate'>🍽️</div>
                  <div className='floating-dots'>
                    <span className='dot dot-1'>•</span>
                    <span className='dot dot-2'>•</span>
                    <span className='dot dot-3'>•</span>
                  </div>
                </div>
                <div className='no-items-content'>
                  <h3 className='no-items-title'>No dishes found</h3>
                  <p className='no-items-text'>
                    We're constantly adding new delicious items to our menu.
                    Check back soon or try adjusting your filters!
                  </p>
                  <button
                    className='refresh-btn'
                    onClick={() => {
                      setFoodTypeFilter('All');
                      setSearchTerm('');
                    }}
                  >
                    <span className='refresh-icon'>🔄</span>
                    <span className='refresh-text'>Reset Filters</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <hr />
    </div>
  );
};

export default ExploreMenu;
