import { useEffect, useState } from 'react';
import './Header.css';

const Header = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Array of vegetarian food images and videos for carousel
  const vegetarianMedia = [
    // Vegetarian Images
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "🌱 Fresh Vegetarian Pizza",
      subtitle: "Loaded with fresh vegetables and herbs"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1540420773420-3366772f4999?ixlib=rb-4.0.3&auto=format&fit=crop&w=2084&q=80",
      title: "🥗 Healthy Green Salad",
      subtitle: "Crisp lettuce with fresh vegetables"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-**********-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1974&q=80",
      title: "🍲 Nutritious Buddha Bowl",
      subtitle: "Quinoa, avocado, and fresh greens"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-**********-a2132b4ba21d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
      title: "🍝 Creamy Vegetarian Pasta",
      subtitle: "Rich tomato sauce with fresh basil"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1506084868230-bb9d95c24759?ixlib=rb-4.0.3&auto=format&fit=crop&w=2067&q=80",
      title: "🥪 Veggie Sandwich Delight",
      subtitle: "Fresh vegetables in artisan bread"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-**********-85f173990554?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "🍜 Asian Vegetable Noodles",
      subtitle: "Stir-fried with fresh vegetables"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1565299507177-b0ac66763828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2080&q=80",
      title: "🌮 Veggie Tacos",
      subtitle: "Colorful vegetables in soft tortillas"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "🥙 Mediterranean Wrap",
      subtitle: "Fresh herbs and vegetables"
    },
    // More Vegetarian Food Images
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1543339494-b4cd4f7ba686?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "🥘 Indian Vegetarian Thali",
      subtitle: "Traditional vegetarian feast"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1592417817098-8fd3d9eb14a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80",
      title: "🍕 Margherita Pizza",
      subtitle: "Classic vegetarian pizza with fresh basil"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1567306301408-9b74779a11af?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      title: "🍛 Veggie Rice Bowl",
      subtitle: "Steamed rice with seasonal vegetables"
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=2072&q=80",
      title: "🥬 Green Smoothie Bowl",
      subtitle: "Packed with nutrients and flavor"
    }
  ];

  // Auto-rotate media every 2 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % vegetarianMedia.length
      );
    }, 2000); // 2 seconds for fast, dynamic display

    return () => clearInterval(interval);
  }, [vegetarianMedia.length]);

  const goToSlide = (index) => {
    setCurrentImageIndex(index);
  };

  const goToPrevious = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? vegetarianMedia.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentImageIndex((prevIndex) =>
      (prevIndex + 1) % vegetarianMedia.length
    );
  };

  return (
    <div className='header'>
      <div className="header-carousel">
        {vegetarianMedia.map((media, index) => (
          <div
            key={index}
            className={`header-slide ${index === currentImageIndex ? 'active' : ''} ${media.type}`}
          >
            {media.type === 'image' ? (
              <div
                className="slide-background"
                style={{ backgroundImage: `url(${media.url})` }}
              ></div>
            ) : (
              <video
                className="slide-video"
                autoPlay
                muted
                loop
                playsInline
              >
                <source src={media.url} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            )}
            <div className="slide-overlay"></div>
            <div className="media-type-indicator">
              {media.type === 'video' ? '🎥' : '📸'}
            </div>
          </div>
        ))}

        {/* Navigation Arrows */}
        <button className="carousel-btn prev-btn" onClick={goToPrevious}>
          &#8249;
        </button>
        <button className="carousel-btn next-btn" onClick={goToNext}>
          &#8250;
        </button>

        {/* Dots Indicator */}
        <div className="carousel-dots">
          {vegetarianMedia.map((media, index) => (
            <button
              key={index}
              className={`dot ${index === currentImageIndex ? 'active' : ''} ${media.type}`}
              onClick={() => goToSlide(index)}
              title={media.title}
            >
              <span className="dot-icon">
                {media.type === 'video' ? '🎥' : '📸'}
              </span>
            </button>
          ))}
        </div>
      </div>

      <div className="header-contents">
        <h2>Order your favourite food here</h2>
        <p>Choose from a diverse menu featuring a delectable array of dishes crafted with the finest ingredients and culinary expertise. Our mission is to satisfy your cravings and elevate your dining experience, one delicious meal at a time.</p>
        <div className="header-buttons">
          <button className="view-menu-btn">🍽️ View Menu</button>
          <button className="order-now-btn">🚀 Order Now</button>
        </div>

        {/* Current slide info */}
        <div className="slide-info">
          <div className="slide-counter">
            {currentImageIndex + 1} / {vegetarianMedia.length}
          </div>
          <h3>{vegetarianMedia[currentImageIndex].title}</h3>
          <p>{vegetarianMedia[currentImageIndex].subtitle}</p>
          <div className="media-type-badge">
            {vegetarianMedia[currentImageIndex].type === 'video' ?
              '🎥 Video Content' : '📸 Image Gallery'
            }
          </div>
        </div>
      </div>
    </div>
  )
}

export default Header
