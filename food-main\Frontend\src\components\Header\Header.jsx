import { useEffect, useState } from 'react';
import './Header.css';

const Header = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Array of food images for carousel
  const foodImages = [
    {
      url: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1981&q=80",
      title: "Delicious Pizza",
      subtitle: "Fresh ingredients, perfect taste"
    },
    {
      url: "https://images.unsplash.com/photo-**********-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      title: "Gourmet Burgers",
      subtitle: "Juicy, flavorful, irresistible"
    },
    {
      url: "https://images.unsplash.com/photo-**********-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80",
      title: "Healthy Bowls",
      subtitle: "Nutritious and delicious"
    },
    {
      url: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1980&q=80",
      title: "Asian Cuisine",
      subtitle: "Authentic flavors from the East"
    },
    {
      url: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      title: "Fresh Salads",
      subtitle: "Crisp, fresh, and healthy"
    }
  ];

  // Auto-rotate images every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % foodImages.length
      );
    }, 10000); // 10 seconds

    return () => clearInterval(interval);
  }, [foodImages.length]);

  const goToSlide = (index) => {
    setCurrentImageIndex(index);
  };

  const goToPrevious = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? foodImages.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentImageIndex((prevIndex) =>
      (prevIndex + 1) % foodImages.length
    );
  };

  return (
    <div className='header'>
      <div className="header-carousel">
        {foodImages.map((image, index) => (
          <div
            key={index}
            className={`header-slide ${index === currentImageIndex ? 'active' : ''}`}
            style={{ backgroundImage: `url(${image.url})` }}
          >
            <div className="slide-overlay"></div>
          </div>
        ))}

        {/* Navigation Arrows */}
        <button className="carousel-btn prev-btn" onClick={goToPrevious}>
          &#8249;
        </button>
        <button className="carousel-btn next-btn" onClick={goToNext}>
          &#8250;
        </button>

        {/* Dots Indicator */}
        <div className="carousel-dots">
          {foodImages.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentImageIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>
      </div>

      <div className="header-contents">
        <h2>Order your favourite food here</h2>
        <p>Choose from a diverse menu featuring a delectable array of dishes crafted with the finest ingredients and culinary expertise. Our mission is to satisfy your cravings and elevate your dining experience, one delicious meal at a time.</p>
        <div className="header-buttons">
          <button className="view-menu-btn">🍽️ View Menu</button>
          <button className="order-now-btn">🚀 Order Now</button>
        </div>

        {/* Current slide info */}
        <div className="slide-info">
          <h3>{foodImages[currentImageIndex].title}</h3>
          <p>{foodImages[currentImageIndex].subtitle}</p>
        </div>
      </div>
    </div>
  )
}

export default Header
