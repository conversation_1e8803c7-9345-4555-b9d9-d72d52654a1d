import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { assets } from "../../assets/assets";
import { StoreContext } from "../../context/StoreContext"; // Import StoreContext
import "./LoginPopup.css";

const LoginPopup = ({ setShowLogin }) => {
  const { setToken, setUserId } = useContext(StoreContext); // Get setToken and setUserId from context
  const [currState, setCurrState] = useState("Login");
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    setMessage(""); // Clear previous messages

    // Validate form data
    if (!formData.email || !formData.password) {
      setMessage("Error: Email and password are required");
      setIsLoading(false);
      return;
    }

    if (currState === "Sign Up" && !formData.name) {
      setMessage("Error: Name is required for registration");
      setIsLoading(false);
      return;
    }

    const url =
      currState === "Sign Up"
        ? "http://localhost:8889/api/auth/signup"
        : "http://localhost:8889/api/auth/signin";

    console.log("Sending request to:", url);
    console.log("Form Data:", JSON.stringify(formData));

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify(formData),
      });

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        throw new Error("Server returned non-JSON response");
      }

      const data = await response.json();

      // Handle non-200 responses
      if (!response.ok) {
        console.error("Server responded with an error:", data);
        throw new Error(data.message || `Server error: ${response.status}`);
      }

      console.log("Response from server:", data);

      if (currState === "Sign Up") {
        setMessage("✅ Registration successful! Please login with your credentials.");
        setTimeout(() => {
          setCurrState("Login");
          setMessage("");
        }, 2000);
      } else {
        // Validate login response
        if (!data.accessToken) {
          throw new Error("Invalid response: Missing access token");
        }

        setMessage("✅ Login successful! Redirecting...");

        // Store authentication data
        localStorage.setItem("token", data.accessToken);
        if (data.userId) {
          localStorage.setItem("userId", data.userId);
          setUserId(data.userId);
        }
        setToken(data.accessToken);

        setTimeout(() => {
          setShowLogin(false);
          navigate("/");
        }, 1500);
      }

      // Reset form data after successful submission
      setFormData({ name: "", email: "", password: "" });
    } catch (error) {
      console.error("Error during signup/login:", error);

      // Handle different types of errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setMessage("❌ Network error: Unable to connect to server. Please check if the backend is running.");
      } else if (error.message.includes('JSON')) {
        setMessage("❌ Server error: Invalid response format");
      } else {
        setMessage(`❌ ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-popup">
      <form className="login-popup-container" onSubmit={handleSubmit}>
        <div className="login-popup-title">
          <h2>{currState}</h2>
          <img onClick={() => setShowLogin(false)} src={assets.cross_icon} alt="Close popup" />
        </div>

        <div className="login-popup-inputs">
          {currState === "Sign Up" && (
            <input
              type="text"
              name="name"
              placeholder="Your Name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          )}
          <input
            type="email"
            name="email"
            placeholder="Your Email"
            value={formData.email}
            onChange={handleChange}
            required
          />
          <input
            type="password"
            name="password"
            placeholder="Your Password"
            value={formData.password}
            onChange={handleChange}
            required
          />
        </div>

        <button type="submit" disabled={isLoading}>
          {isLoading ? "Processing..." : currState === "Sign Up" ? "Create Account" : "Log In"}
        </button>

        <div className="login-popup-condition">
          <input type="checkbox" required />
          <p>By continuing I agree to the terms of use & privacy policy</p>
        </div>

        {currState === "Login" ? (
          <p>
            Create a new account?{" "}
            <span onClick={() => setCurrState("Sign Up")}>Click here</span>
          </p>
        ) : (
          <p>
            Already have an account?{" "}
            <span onClick={() => setCurrState("Login")}>Click here</span>
          </p>
        )}
      </form>

      {message && (
        <div className={`login-message ${message.includes("❌") ? "error-message" : "success-message"}`}>
          {message}
        </div>
      )}

      {message.includes("Incorrect email or password") && (
        <div className="forgot-password">
          <p>
            Forgot Password?{" "}
            <span onClick={() => navigate("/forgot-password")}>Click here</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default LoginPopup;
