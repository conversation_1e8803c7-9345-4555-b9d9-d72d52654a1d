/* Modern Food Detail Styles */
.modern-food-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading and Error States */
.loading-container,
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 60px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.not-found-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.not-found-container h2 {
  font-size: 32px;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.not-found-container p {
  font-size: 18px;
  color: #7f8c8d;
  margin: 0 0 30px 0;
}

.back-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Header Section */
.detail-header {
  max-width: 1200px;
  margin: 0 auto 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 2px solid #e9ecef;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
}

.back-button:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.back-icon {
  font-size: 18px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7f8c8d;
  font-size: 14px;
}

.separator {
  color: #bdc3c7;
}

/* Main Content Layout */
.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  padding: 0 20px;
}

/* Image Section */
.image-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  background: white;
  padding: 10px;
}

.main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.food-type-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 14px;
  font-weight: 600;
}

.type-indicator.veg {
  color: #27ae60;
}

.type-indicator.non-veg {
  color: #e74c3c;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
  opacity: 0.7;
}

.thumbnail:hover {
  opacity: 1;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #667eea;
  opacity: 1;
  transform: scale(1.05);
}

/* Details Section */
.details-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.product-header {
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 20px;
}

.product-title {
  font-size: clamp(28px, 4vw, 36px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 18px;
  color: #ddd;
}

.star.filled {
  color: #ffc107;
}

.rating-text {
  color: #7f8c8d;
  font-size: 14px;
}

.category-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .Food-detail-container {
    flex-direction: column; /* Stacks on smaller screens */
    align-items: center;
  }

  .Food-detail-image-container {
    max-width: 100%;
    text-align: center;
  }

  .Food-detail-image {
    height: auto;
    max-width: 100%;
  }

  .Food-detail-details {
    max-width: 100%;
    padding: 12px;
    margin-top: 10px;
  }
}

/* Small screen adjustments */
@media (max-width: 480px) {
  .Food-detail-image {
    height: auto;
    max-width: 100%;
  }

  .Food-detail-title {
    font-size: 18px;
  }

  .Food-detail-price {
    font-size: 14px;
  }

  .Food-detail-desc {
    font-size: 12px;
  }
}
/* See More Button */
.see-more-btn {
  background: none;
  border: none;
  color: blue;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.see-more-btn:hover {
  color: darkblue;
}

/* Price Section */
.price-section {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.current-price {
  font-size: 32px;
  font-weight: 700;
  color: #27ae60;
}

.original-price {
  font-size: 20px;
  color: #7f8c8d;
  text-decoration: line-through;
}

.discount-badge {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Section Styles */
.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.description-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  border-left: 4px solid #667eea;
}

.description-text {
  font-size: 16px;
  line-height: 1.7;
  color: #555;
  margin: 0;
}

.toggle-desc-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-weight: 600;
  margin-left: 5px;
  text-decoration: underline;
  font-size: 14px;
}

.toggle-desc-btn:hover {
  color: #5a67d8;
}

/* Features Section */
.features-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 20px;
}

.feature-text {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

/* Quantity Section */
.quantity-section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.qty-btn {
  width: 45px;
  height: 45px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #2c3e50;
}

.qty-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  transform: scale(1.05);
}

.qty-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  min-width: 40px;
  text-align: center;
}

.quantity-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.total-price {
  font-size: 20px;
  font-weight: 700;
  color: #27ae60;
}

.cart-info {
  font-size: 14px;
  color: #7f8c8d;
  font-style: italic;
}

/* Action Section */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.add-to-cart-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 18px 30px;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-to-cart-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.cart-icon {
  font-size: 20px;
}

.btn-price {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 14px;
}

.secondary-actions {
  display: flex;
  gap: 15px;
}

.wishlist-btn,
.share-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: white;
  color: #2c3e50;
  padding: 15px 20px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.wishlist-btn:hover {
  border-color: #e74c3c;
  color: #e74c3c;
  transform: translateY(-2px);
}

.share-btn:hover {
  border-color: #3498db;
  color: #3498db;
  transform: translateY(-2px);
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .detail-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .detail-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .breadcrumb {
    order: -1;
  }

  .main-image {
    height: 300px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .modern-food-detail {
    padding: 15px;
  }

  .detail-header {
    margin-bottom: 20px;
    padding: 0 10px;
  }

  .detail-content {
    gap: 30px;
    padding: 0 10px;
  }

  .product-title {
    font-size: 28px;
  }

  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .price-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .current-price {
    font-size: 28px;
  }

  .image-thumbnails {
    gap: 8px;
  }

  .thumbnail {
    width: 60px;
    height: 60px;
  }

  .description-section,
  .features-section,
  .quantity-section {
    padding: 20px;
  }

  .secondary-actions {
    flex-direction: column;
  }

  .add-to-cart-btn {
    padding: 15px 25px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .modern-food-detail {
    padding: 10px;
  }

  .detail-header {
    padding: 0 5px;
  }

  .detail-content {
    padding: 0 5px;
  }

  .back-button {
    padding: 10px 15px;
    font-size: 14px;
  }

  .product-title {
    font-size: 24px;
  }

  .main-image {
    height: 250px;
  }

  .current-price {
    font-size: 24px;
  }

  .image-thumbnails {
    gap: 5px;
  }

  .thumbnail {
    width: 50px;
    height: 50px;
  }

  .quantity-controls {
    justify-content: center;
  }

  .qty-btn {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .quantity-display {
    font-size: 20px;
  }

  .description-section,
  .features-section,
  .quantity-section {
    padding: 15px;
  }

  .section-title {
    font-size: 18px;
  }

  .add-to-cart-btn {
    padding: 12px 20px;
    font-size: 14px;
  }

  .wishlist-btn,
  .share-btn {
    padding: 12px 15px;
    font-size: 13px;
  }
}
