/* Modern Food Detail Styles */
.Food-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
  display: flex;
  gap: 40px;
  align-items: flex-start;
  min-height: 80vh;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Image Container */
.Food-detail-image-container {
  flex: 1;
  max-width: 500px;
  position: sticky;
  top: 24px;
}

.Food-detail-image-container img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.Food-detail-image-container img:hover {
  transform: scale(1.02);
}

/* Details Container */
.Food-detail-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 20px 0;
}

/* Food Title */
.Food-detail-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.Food-detail-label {
  color: #3b82f6;
  font-weight: 600;
  font-size: 16px;
  margin-right: 8px;
}

/* Description */
.Food-detail-desc {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
  margin: 0 0 20px 0;
  background: #f7fafc;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
}

.see-more-btn {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  font-size: 14px;
  margin-left: 4px;
  transition: color 0.2s ease;
}

.see-more-btn:hover {
  color: #2563eb;
}

/* Rating */
.Food-detail-rating {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 20px 0;
  padding: 16px;
  background: #fff7ed;
  border-radius: 12px;
  border: 1px solid #fed7aa;
}

.Food-detail-rating img {
  height: 24px;
  width: auto;
}

/* Price */
.Food-detail-price {
  font-size: 24px;
  font-weight: 700;
  color: #059669;
  margin: 0 0 24px 0;
  padding: 20px;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-radius: 12px;
  border: 1px solid #a7f3d0;
  text-align: center;
}

/* Add to Cart Button */
.btn {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  min-width: 200px;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .Food-detail-container {
    flex-direction: column;
    gap: 32px;
    padding: 32px 20px;
  }

  .Food-detail-image-container {
    position: static;
    max-width: 100%;
  }

  .Food-detail-image-container img {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .Food-detail-container {
    padding: 24px 16px;
    gap: 24px;
  }

  .Food-detail-title {
    font-size: 28px;
  }

  .Food-detail-image-container img {
    height: 300px;
  }

  .Food-detail-desc {
    padding: 16px;
  }

  .Food-detail-rating {
    padding: 12px;
  }

  .Food-detail-price {
    font-size: 20px;
    padding: 16px;
  }

  .btn {
    padding: 14px 24px;
    font-size: 15px;
    min-width: 180px;
  }
}

@media (max-width: 480px) {
  .Food-detail-container {
    padding: 20px 12px;
    gap: 20px;
  }

  .Food-detail-title {
    font-size: 24px;
  }

  .Food-detail-label {
    font-size: 14px;
  }

  .Food-detail-image-container img {
    height: 250px;
    border-radius: 12px;
  }

  .Food-detail-desc {
    font-size: 15px;
    padding: 14px;
  }

  .Food-detail-rating {
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .Food-detail-price {
    font-size: 18px;
    padding: 14px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 14px;
    min-width: 160px;
    width: 100%;
  }
}

/* Loading and Error States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  font-size: 18px;
  color: #6b7280;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #dc2626;
}

.error-state h2 {
  font-size: 24px;
  margin-bottom: 8px;
}

.error-state p {
  font-size: 16px;
  color: #6b7280;
}

/* Additional Enhancements */
.Food-detail-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  z-index: 10;
}

/* Smooth Animations */
.Food-detail-title,
.Food-detail-desc,
.Food-detail-rating,
.Food-detail-price,
.btn {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.Food-detail-title {
  animation-delay: 0.1s;
}

.Food-detail-desc {
  animation-delay: 0.2s;
}

.Food-detail-rating {
  animation-delay: 0.3s;
}

.Food-detail-price {
  animation-delay: 0.4s;
}

.btn {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Image Loading Animation */
.Food-detail-image-container img {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Hover Effects */
.Food-detail-desc:hover {
  background: #edf2f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.Food-detail-rating:hover {
  background: #fff3cd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.Food-detail-price:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.2);
}



