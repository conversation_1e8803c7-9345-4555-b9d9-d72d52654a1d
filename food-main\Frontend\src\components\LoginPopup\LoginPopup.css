/* Overlay for the popup */
.login-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7); /* Dark background overlay */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Popup stays on top */
  }
  
  /* Container for the popup form */
  .login-popup-container {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
  }
  
  /* Title section with heading and close button */
  .login-popup-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .login-popup-title h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }
  
  .login-popup-title img {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
  
  /* Input fields styling */
  .login-popup-inputs input {
    width: 100%;
    padding: 0.75rem;
    margin: 0.5rem 0;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1rem;
  }
  
  .login-popup-inputs input:focus {
    border-color: #007BFF; /* Blue border on focus */
    outline: none;
  }
  
  /* Button styling */
  button {
    width: 100%;
    padding: 0.75rem;
    background-color: #007BFF;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 1rem;
  }
  
  button:hover {
    background-color: #0056b3;
  }
  
  /* Checkbox and terms */
  .logoin-popup-condition {
    display: flex;
    align-items: center;
    margin-top: 1rem;
  }
  
  .logoin-popup-condition input {
    margin-right: 0.5rem;
  }
  
  .logoin-popup-condition p {
    font-size: 0.85rem;
    color: #666;
  }
  
  /* Link to switch between login and sign-up */
  .login-popup p {
    font-size: 0.9rem;
    color: #333;
    margin-top: 1rem;
    text-align: center;
  }
  
  .login-popup p span {
    color: #007BFF;
    cursor: pointer;
    text-decoration: underline;
  }
  
  .login-popup p span:hover {
    color: #0056b3;
  }

  /* Message styling */
  .login-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    animation: fadeIn 0.3s ease-in;
  }

  .success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Loading state for button */
  button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
  }

  /* Fix checkbox styling */
  .login-popup-condition {
    display: flex;
    align-items: center;
    margin-top: 1rem;
  }

  .login-popup-condition input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    margin-top: 0;
    margin-bottom: 0;
  }

  .login-popup-condition p {
    font-size: 0.85rem;
    color: #666;
    margin: 0;
  }

  /* Responsive styling for smaller screens */
  @media (max-width: 768px) {
    .login-popup-container {
      width: 95%;
    }
  
    .login-popup-title h2 {
      font-size: 1.2rem;
    }
  
    .login-popup-inputs input {
      font-size: 0.9rem;
    }
  
    button {
      font-size: 0.9rem;
    }
  
    .login-popup p {
      font-size: 0.85rem;
    }
  }
  