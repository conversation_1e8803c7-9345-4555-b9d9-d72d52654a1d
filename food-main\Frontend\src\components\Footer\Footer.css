/* Compact Mobile-First Footer */
.modern-footer {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #f1f5f9;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin-top: 30px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Compact Main Footer Content */
.footer-main {
  padding: 24px 0 16px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  text-align: center;
}

/* Compact Brand Section */
.footer-brand {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 12px;
}

.footer-logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-name {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.brand-tagline {
  font-size: 11px;
  color: #94a3b8;
  margin: 0;
  font-weight: 500;
}

.brand-description {
  font-size: 13px;
  line-height: 1.4;
  color: #cbd5e0;
  margin: 8px 0 12px 0;
  max-width: 280px;
  margin-left: auto;
  margin-right: auto;
}

/* Compact Horizontal Social Links */
.social-links {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap !important;
  padding: 8px 0;
  margin: 0;
}

.social-link {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.15);
  flex-shrink: 0;
  position: relative;
}

.social-link:hover {
  transform: translateY(-2px) scale(1.05);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.social-link:active {
  transform: translateY(0) scale(1);
}

.social-icon {
  width: 18px;
  height: 18px;
  position: relative;
}

.social-text {
  display: none; /* Hide text for cleaner icon-only design */
}

/* Simple brand colors on hover */
.social-link.facebook:hover {
  background: #1877f2;
  border-color: #1877f2;
}

.social-link.instagram:hover {
  background: #e4405f;
  border-color: #e4405f;
}

.social-link.twitter:hover {
  background: #1da1f2;
  border-color: #1da1f2;
}

.social-link.youtube:hover {
  background: #ff0000;
  border-color: #ff0000;
}

.social-link.linkedin:hover {
  background: #0077b5;
  border-color: #0077b5;
}

/* Force horizontal layout - Override any column styles */
.footer-brand .social-links,
.social-links,
div .social-links {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure social links stay inline */
.social-link {
  display: inline-flex !important;
  flex-shrink: 0 !important;
}

/* Compact Footer Links Section */
.footer-section {
  padding: 12px;
  text-align: center;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.footer-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  text-decoration: none;
  color: #94a3b8;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.footer-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.link-icon {
  font-size: 12px;
  width: 12px;
}

.link-text {
  font-size: 12px;
}

/* Compact Contact Info */
.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.contact-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: #94a3b8;
  transition: all 0.3s ease;
}

.contact-item:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.contact-icon {
  font-size: 12px;
  width: 12px;
  color: #3b82f6;
}

.contact-details {
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-label {
  display: none; /* Hide labels for compact design */
}

.contact-value {
  font-size: 12px;
  color: inherit;
  font-weight: 500;
}

/* Compact Newsletter Section */
.footer-newsletter {
  background: rgba(0, 0, 0, 0.2);
  padding: 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 12px;
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.newsletter-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
}

.newsletter-info {
  width: 100%;
}

.newsletter-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.newsletter-description {
  font-size: 12px;
  color: #94a3b8;
  margin: 0;
  line-height: 1.4;
}

.newsletter-form {
  width: 100%;
  max-width: 280px;
}

.email-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.email-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 12px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.email-input::placeholder {
  color: #94a3b8;
}

.email-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.15);
}

.subscribe-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.subscribe-btn:hover {
  background: #2563eb;
}

.btn-text {
  font-size: 12px;
}

.btn-icon {
  display: none; /* Hide icon for compact design */
}

/* Compact Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 8px;
}

.copyright-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.copyright-text {
  font-size: 11px;
  color: #94a3b8;
  margin: 0;
}

.company-info {
  font-size: 10px;
  color: #64748b;
  margin: 0;
}

.legal-links {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.legal-link {
  font-size: 10px;
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 2px 6px;
  border-radius: 3px;
}

.legal-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

/* Desktop Responsive Styles */
@media (min-width: 768px) {
  .footer-main {
    padding: 40px 0 24px;
  }

  .footer-container {
    padding: 0 24px;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 32px;
    align-items: start;
    text-align: left;
  }

  .footer-brand {
    text-align: left;
    padding: 16px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .brand-logo {
    justify-content: flex-start;
  }

  .footer-logo {
    width: 36px;
    height: 36px;
  }

  .brand-name {
    font-size: 20px;
  }

  .brand-description {
    max-width: none;
    margin-left: 0;
    margin-right: 0;
    font-size: 14px;
  }

  .social-links {
    justify-content: flex-start !important;
    gap: 16px;
    padding: 12px 0;
  }

  .social-link {
    width: 44px;
    height: 44px;
  }

  .social-icon {
    width: 20px;
    height: 20px;
  }

  .footer-section {
    text-align: left;
    padding: 0;
  }

  .section-title {
    font-size: 15px;
  }

  .footer-links {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .footer-link {
    justify-content: flex-start;
    padding: 4px 0;
    background: none;
    font-size: 13px;
  }

  .contact-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .contact-item {
    justify-content: flex-start;
    padding: 4px 0;
    font-size: 13px;
  }

  .newsletter-content {
    flex-direction: row;
    text-align: left;
    gap: 24px;
  }

  .newsletter-title {
    font-size: 16px;
  }

  .newsletter-description {
    font-size: 13px;
  }

  .newsletter-form {
    max-width: 300px;
  }

  .footer-bottom-container {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
    gap: 16px;
  }

  .copyright-section {
    align-items: flex-start;
  }

  .copyright-text {
    font-size: 12px;
  }

  .company-info {
    font-size: 11px;
  }

  .legal-link {
    font-size: 11px;
  }
}

@media (min-width: 1024px) {
  .footer-container {
    gap: 40px;
  }

  .brand-name {
    font-size: 22px;
  }

  .brand-description {
    font-size: 15px;
  }

  .section-title {
    font-size: 16px;
  }

  .newsletter-title {
    font-size: 18px;
  }

  .newsletter-description {
    font-size: 14px;
  }
}

/* Mobile Optimizations - Already optimized for mobile-first */
@media (max-width: 480px) {
  .footer-main {
    padding: 20px 0 12px;
  }

  .footer-container {
    gap: 16px;
  }

  .social-links {
    gap: 10px;
    padding: 6px 0;
  }

  .social-link {
    width: 36px;
    height: 36px;
  }

  .social-icon {
    width: 16px;
    height: 16px;
  }

  .newsletter-container {
    padding: 0 12px;
  }

  .email-input {
    font-size: 11px;
    padding: 6px 10px;
  }

  .subscribe-btn {
    font-size: 11px;
    padding: 6px 12px;
  }
}

@media (max-width: 360px) {
  .footer-container {
    padding: 0 12px;
    gap: 12px;
  }

  .footer-brand {
    padding: 12px;
  }

  .brand-name {
    font-size: 16px;
  }

  .brand-description {
    font-size: 12px;
  }

  .social-link {
    width: 32px;
    height: 32px;
  }

  .social-icon {
    width: 14px;
    height: 14px;
  }

  .section-title {
    font-size: 13px;
  }

  .newsletter-title {
    font-size: 13px;
  }
}
