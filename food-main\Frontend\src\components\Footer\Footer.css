.footer {
    background-color: #fff;
    color: #282020;
    padding: 40px 20px;
    text-align: center;
    border-top: 1px solid #eee;
    margin-top: 50px;
}

.footer-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: auto;
    text-align: left;
}

.footer-left, .footer-center, .footer-right {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-left img {
    width: 120px;
    margin-bottom: 15px;
}

.footer-left p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.footer-social img {
    background-color: #60575e;
    width: 35px;
    cursor: pointer;
    transition: transform 0.3s;
}

.footer-social img:hover {
    transform: scale(1.1);
}

.footer-center ul, .footer-right ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-center li, .footer-right li {
    margin-bottom: 10px;
}

hr {
    margin: 30px 0;
    border: 0.5px solid #ee2a30;
}

.footer-copyright {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .footer {
        padding: 30px 15px;
        text-align: center;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: 25px;
        text-align: center;
    }

    .footer-left, .footer-center, .footer-right {
        align-items: center;
        text-align: center;
    }

    .footer-left img {
        width: 100px;
        margin-bottom: 12px;
    }

    .footer-left p {
        font-size: 14px;
        margin-bottom: 15px;
        max-width: 300px;
    }

    .footer-social {
        justify-content: center;
        gap: 12px;
    }

    .footer-social img {
        width: 32px;
        height: 32px;
    }

    .footer-center h3, .footer-right h3 {
        font-size: 18px;
        margin-bottom: 15px;
        color: #333;
    }

    .footer-center li, .footer-right li {
        margin-bottom: 8px;
        font-size: 14px;
    }

    hr {
        margin: 25px 0;
    }

    .footer-copyright {
        font-size: 13px;
        padding: 0 10px;
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 25px 10px;
    }

    .footer-container {
        gap: 20px;
    }

    .footer-left img {
        width: 80px;
        margin-bottom: 10px;
    }

    .footer-left p {
        font-size: 13px;
        margin-bottom: 12px;
        line-height: 1.5;
    }

    .footer-social {
        gap: 10px;
    }

    .footer-social img {
        width: 28px;
        height: 28px;
    }

    .footer-center h3, .footer-right h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .footer-center li, .footer-right li {
        margin-bottom: 6px;
        font-size: 13px;
    }

    hr {
        margin: 20px 0;
    }

    .footer-copyright {
        font-size: 12px;
        line-height: 1.4;
    }
}

@media (max-width: 360px) {
    .footer {
        padding: 20px 8px;
    }

    .footer-left p {
        font-size: 12px;
    }

    .footer-social img {
        width: 26px;
        height: 26px;
    }

    .footer-center h3, .footer-right h3 {
        font-size: 15px;
    }

    .footer-center li, .footer-right li {
        font-size: 12px;
    }

    .footer-copyright {
        font-size: 11px;
    }
}
