/* Modern Footer Styles */
.modern-footer {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: #e2e8f0;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin-top: 60px;
}

/* Main Footer Content */
.footer-main {
  padding: 60px 0 40px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 40px;
  align-items: start;
}

/* Brand Section */
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Ensure social links stay horizontal within brand section */
.footer-brand .social-links {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.footer-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.brand-tagline {
  font-size: 14px;
  color: #a0aec0;
  margin: 0;
}

.brand-description {
  font-size: 15px;
  line-height: 1.6;
  color: #cbd5e0;
  margin: 0;
}

/* Social Links - Force Single Row */
.social-links {
  display: flex !important;
  flex-direction: row !important;
  gap: 16px;
  flex-wrap: nowrap !important;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-decoration: none;
  color: #e2e8f0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: fit-content;
  white-space: nowrap;
}

/* Uniform hover colors for all social links */
.social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.social-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.social-text {
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

/* Override any column layout for social icons */
.social-links,
.footer-brand .social-links,
.footer-section .social-links {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
}

/* Ensure social icons display horizontally */
.social-link {
  display: inline-flex !important;
  flex-shrink: 0;
}

/* Footer Sections */
.footer-section {
  display: flex;
  gap: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #cbd5e0;
  transition: all 0.3s ease;
  padding: 8px 0;
}

.footer-link:hover {
  color: #ffffff;
  transform: translateX(4px);
}

.link-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.link-text {
  font-size: 14px;
  font-weight: 500;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.contact-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-top: 2px;
  flex-shrink: 0;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-label {
  font-size: 12px;
  font-weight: 600;
  color: #a0aec0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 14px;
  color: #e2e8f0;
  line-height: 1.4;
}

/* Newsletter Section */
.footer-newsletter {
  background: rgba(0, 0, 0, 0.2);
  padding: 40px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.newsletter-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.newsletter-info {
  flex: 1;
}

.newsletter-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.newsletter-description {
  font-size: 16px;
  color: #cbd5e0;
  margin: 0;
  line-height: 1.5;
}

.newsletter-form {
  flex: 1;
  max-width: 400px;
}

.email-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.email-input {
  flex: 1;
  padding: 14px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.email-input::placeholder {
  color: #a0aec0;
}

.email-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.subscribe-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-text {
  font-size: 14px;
}

.btn-icon {
  font-size: 16px;
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  padding: 24px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.copyright-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.copyright-text {
  font-size: 14px;
  color: #cbd5e0;
  margin: 0;
}

.company-info {
  font-size: 13px;
  color: #a0aec0;
  margin: 0;
}

.legal-links {
  display: flex;
  gap: 24px;
  align-items: center;
}

.legal-link {
  font-size: 14px;
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.legal-link:hover {
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-container {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }

  .footer-brand {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .footer-main {
    padding: 40px 0 30px;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 32px;
    text-align: center;
  }

  .brand-logo {
    justify-content: center;
  }

  .social-links {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center;
    flex-wrap: nowrap !important;
    overflow-x: auto;
    padding: 0 8px;
    width: 100%;
  }

  .footer-section {
    text-align: center;
    flex-direction: column;
  }

  .footer-links {
    align-items: center;
    flex-direction: column;
  }

  .contact-info {
    align-items: center;
    flex-direction: column;
  }

  .contact-item {
    justify-content: center;
    text-align: left;
  }

  .newsletter-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .newsletter-form {
    max-width: 100%;
  }

  .footer-bottom-container {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .legal-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-main {
    padding: 32px 0 24px;
  }

  .footer-container {
    padding: 0 16px;
    gap: 24px;
  }

  .brand-name {
    font-size: 20px;
  }

  .brand-description {
    font-size: 14px;
  }

  .social-links {
    display: flex !important;
    flex-direction: row !important;
    gap: 12px;
    justify-content: center;
    flex-wrap: nowrap !important;
    overflow-x: auto;
    padding: 0 16px;
    width: 100%;
  }

  .social-link {
    padding: 10px 12px;
    font-size: 13px;
    min-width: 44px;
  }

  .social-text {
    display: none;
  }

  .social-icon {
    width: 18px;
    height: 18px;
  }

  .section-title {
    font-size: 16px;
  }

  .footer-link {
    font-size: 14px;
  }

  .contact-value {
    font-size: 13px;
  }

  .newsletter-title {
    font-size: 20px;
  }

  .newsletter-description {
    font-size: 14px;
  }

  .email-input-group {
    flex-direction: column;
    gap: 12px;
  }

  .email-input,
  .subscribe-btn {
    width: 100%;
    justify-content: center;
  }

  .footer-bottom {
    padding: 20px 0;
  }

  .footer-bottom-container {
    padding: 0 16px;
  }

  .legal-links {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 360px) {
  .footer-container {
    padding: 0 12px;
  }

  .brand-name {
    font-size: 18px;
  }

  .brand-description {
    font-size: 13px;
  }

  .social-link {
    padding: 8px 10px;
    min-width: 40px;
  }

  .social-icon {
    width: 16px;
    height: 16px;
  }

  .section-title {
    font-size: 15px;
  }

  .newsletter-title {
    font-size: 18px;
  }

  .copyright-text,
  .legal-link {
    font-size: 13px;
  }

  .company-info {
    font-size: 12px;
  }
}
