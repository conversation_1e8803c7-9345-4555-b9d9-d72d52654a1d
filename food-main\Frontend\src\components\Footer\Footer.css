/* Modern Footer Styles */
.modern-footer {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: #e2e8f0;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin-top: 60px;
}

/* Main Footer Content */
.footer-main {
  padding: 60px 0 40px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 40px;
  align-items: start;
}

/* Brand Section */
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.footer-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.brand-tagline {
  font-size: 14px;
  color: #a0aec0;
  margin: 0;
}

.brand-description {
  font-size: 16px;
  line-height: 1.6;
  color: #cbd5e0;
  margin: 0;
}

.social-links {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  text-decoration: none;
  color: #e2e8f0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.social-icon {
  font-size: 16px;
}

.social-text {
  font-size: 14px;
  font-weight: 500;
}

/* Footer Sections */
.footer-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  text-decoration: none;
  color: #cbd5e0;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.footer-link:hover {
  color: #ffffff;
  transform: translateX(4px);
}

.link-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.link-text {
  font-size: 15px;
  font-weight: 500;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
}

.contact-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
  margin-top: 2px;
  color: #667eea;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-label {
  font-size: 13px;
  font-weight: 600;
  color: #a0aec0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 15px;
  color: #e2e8f0;
  line-height: 1.4;
}

/* Newsletter Section */
.footer-newsletter {
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px 0;
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.newsletter-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.newsletter-info {
  flex: 1;
}

.newsletter-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.newsletter-description {
  font-size: 16px;
  color: #cbd5e0;
  margin: 0;
  line-height: 1.5;
}

.newsletter-form {
  flex: 1;
  max-width: 400px;
}

.email-input-group {
  display: flex;
  gap: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.email-input {
  flex: 1;
  padding: 14px 16px;
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 15px;
  outline: none;
  border-radius: 8px;
}

.email-input::placeholder {
  color: #a0aec0;
}

.subscribe-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-text {
  font-weight: 600;
}

.btn-icon {
  font-size: 16px;
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px 0;
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.copyright-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.copyright-text {
  font-size: 14px;
  color: #cbd5e0;
  margin: 0;
}

.company-info {
  font-size: 13px;
  color: #a0aec0;
  margin: 0;
}

.legal-links {
  display: flex;
  gap: 24px;
  align-items: center;
}

.legal-link {
  font-size: 14px;
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.legal-link:hover {
  color: #ffffff;
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .footer-container {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }

  .footer-brand {
    grid-column: 1 / -1;
  }

  .newsletter-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .newsletter-form {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .footer-main {
    padding: 40px 0 32px;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 0 20px;
  }

  .footer-brand {
    grid-column: 1;
    text-align: center;
  }

  .brand-logo {
    justify-content: center;
  }

  .social-links {
    justify-content: center;
  }

  .footer-section {
    text-align: center;
  }

  .footer-links {
    align-items: center;
  }

  .footer-link {
    justify-content: center;
    padding: 10px 0;
  }

  .contact-info {
    align-items: center;
  }

  .contact-item {
    justify-content: center;
    text-align: center;
  }

  .newsletter-container {
    padding: 0 20px;
  }

  .newsletter-content {
    gap: 20px;
  }

  .email-input-group {
    flex-direction: column;
    gap: 8px;
    padding: 8px;
  }

  .email-input {
    padding: 12px 16px;
  }

  .subscribe-btn {
    padding: 12px 20px;
    justify-content: center;
  }

  .footer-bottom-container {
    flex-direction: column;
    gap: 16px;
    padding: 0 20px;
    text-align: center;
  }

  .legal-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer-main {
    padding: 32px 0 24px;
  }

  .footer-container {
    gap: 24px;
    padding: 0 16px;
  }

  .brand-name {
    font-size: 20px;
  }

  .brand-description {
    font-size: 14px;
  }

  .social-links {
    gap: 8px;
  }

  .social-link {
    padding: 8px 12px;
    font-size: 13px;
  }

  .social-text {
    display: none;
  }

  .section-title {
    font-size: 16px;
  }

  .footer-link {
    padding: 8px 0;
  }

  .link-text {
    font-size: 14px;
  }

  .contact-item {
    padding: 8px 0;
  }

  .contact-value {
    font-size: 14px;
  }

  .footer-newsletter {
    padding: 32px 0;
  }

  .newsletter-container {
    padding: 0 16px;
  }

  .newsletter-title {
    font-size: 20px;
  }

  .newsletter-description {
    font-size: 14px;
  }

  .email-input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .subscribe-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .footer-bottom {
    padding: 20px 0;
  }

  .footer-bottom-container {
    padding: 0 16px;
  }

  .legal-links {
    flex-direction: column;
    gap: 12px;
  }

  .legal-link {
    font-size: 13px;
  }
}
