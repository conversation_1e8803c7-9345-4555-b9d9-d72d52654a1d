/* Mobile-First Modern Footer */
.modern-footer {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #f1f5f9;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin-top: 40px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Main Footer Content - Mobile First */
.footer-main {
  padding: 40px 0 20px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Mobile-First Brand Section */
.footer-brand {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.footer-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.brand-name {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-tagline {
  font-size: 12px;
  color: #cbd5e0;
  margin: 0;
  font-weight: 500;
}

.brand-description {
  font-size: 14px;
  line-height: 1.5;
  color: #e2e8f0;
  margin: 0 0 20px 0;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile-First Social Links */
.social-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 8px;
  margin: 0 -8px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-decoration: none;
  color: #f1f5f9;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.social-link:hover::before {
  transform: translateX(100%);
}

.social-link:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.social-link:active {
  transform: translateY(0) scale(0.95);
}

.social-icon {
  width: 18px;
  height: 18px;
  z-index: 1;
  position: relative;
}

.social-text {
  display: none; /* Hide text on mobile for cleaner look */
}

/* Mobile-First Footer Sections */
.footer-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  padding-bottom: 8px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.footer-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  color: #cbd5e0;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  min-width: 120px;
}

.footer-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.link-icon {
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.link-text {
  font-size: 13px;
  font-weight: 500;
}

/* Mobile-First Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-radius: 8px;
  min-width: 200px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  color: #3b82f6;
  flex-shrink: 0;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

.contact-label {
  font-size: 10px;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 13px;
  color: #e2e8f0;
  line-height: 1.3;
  font-weight: 500;
}

/* Mobile-First Newsletter Section */
.footer-newsletter {
  background: rgba(0, 0, 0, 0.3);
  padding: 30px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 20px;
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.newsletter-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 20px;
}

.newsletter-info {
  width: 100%;
}

.newsletter-title {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.newsletter-description {
  font-size: 14px;
  color: #cbd5e0;
  margin: 0;
  line-height: 1.5;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.newsletter-form {
  width: 100%;
  max-width: 350px;
}

.email-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: stretch;
}

.email-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.email-input::placeholder {
  color: #94a3b8;
}

.email-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.subscribe-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.subscribe-btn:active {
  transform: translateY(0);
}

.btn-text {
  font-size: 14px;
}

.btn-icon {
  font-size: 16px;
}

/* Mobile-First Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.4);
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
}

.copyright-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.copyright-text {
  font-size: 13px;
  color: #cbd5e0;
  margin: 0;
}

.company-info {
  font-size: 12px;
  color: #94a3b8;
  margin: 0;
}

.legal-links {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.legal-link {
  font-size: 12px;
  color: #cbd5e0;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.legal-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

/* Desktop Responsive Styles */
@media (min-width: 768px) {
  .footer-main {
    padding: 60px 0 40px;
  }

  .footer-container {
    padding: 0 24px;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 40px;
    align-items: start;
  }

  .footer-brand {
    text-align: left;
    padding: 0;
    background: none;
    border: none;
  }

  .brand-logo {
    justify-content: flex-start;
  }

  .brand-description {
    max-width: none;
    margin-left: 0;
    margin-right: 0;
  }

  .social-links {
    justify-content: flex-start;
    margin: 0;
  }

  .footer-section {
    text-align: left;
    background: none;
    border: none;
    padding: 0;
  }

  .section-title::after {
    left: 0;
    transform: none;
  }

  .footer-links {
    align-items: flex-start;
  }

  .footer-link {
    justify-content: flex-start;
    min-width: auto;
  }

  .contact-info {
    align-items: flex-start;
  }

  .contact-item {
    justify-content: flex-start;
    min-width: auto;
  }

  .newsletter-content {
    flex-direction: row;
    text-align: left;
    gap: 40px;
  }

  .newsletter-info {
    flex: 1;
  }

  .newsletter-description {
    max-width: none;
    margin-left: 0;
    margin-right: 0;
  }

  .newsletter-form {
    flex: 1;
    max-width: 400px;
  }

  .email-input-group {
    flex-direction: row;
  }

  .subscribe-btn {
    width: auto;
    white-space: nowrap;
  }

  .footer-bottom-container {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }

  .copyright-section {
    align-items: flex-start;
  }
}

@media (min-width: 1024px) {
  .footer-container {
    gap: 50px;
  }

  .brand-name {
    font-size: 24px;
  }

  .brand-tagline {
    font-size: 14px;
  }

  .brand-description {
    font-size: 15px;
  }

  .section-title {
    font-size: 18px;
  }

  .newsletter-title {
    font-size: 24px;
  }

  .newsletter-description {
    font-size: 16px;
  }
}

/* Additional Mobile Optimizations */
@media (max-width: 480px) {
  .footer-main {
    padding: 30px 0 20px;
  }

  .footer-container {
    gap: 24px;
  }

  .social-link {
    width: 40px;
    height: 40px;
  }

  .social-icon {
    width: 16px;
    height: 16px;
  }

  .newsletter-container {
    padding: 0 12px;
  }

  .email-input {
    font-size: 13px;
  }

  .subscribe-btn {
    font-size: 13px;
    padding: 10px 20px;
  }
}

@media (max-width: 360px) {
  .footer-container {
    padding: 0 12px;
    gap: 20px;
  }

  .social-link {
    width: 36px;
    height: 36px;
  }

  .social-icon {
    width: 14px;
    height: 14px;
  }

  .brand-name {
    font-size: 18px;
  }

  .section-title {
    font-size: 15px;
  }

  .newsletter-title {
    font-size: 16px;
  }
}
