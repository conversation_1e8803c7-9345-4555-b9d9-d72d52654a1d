import mongoose from "mongoose";

export const connectDB = async () => {
    try {
        // Use environment variable for database connection string
        const mongoURI = process.env.MONGODB_URI || "mongodb://localhost:27017/food-del";
        await mongoose.connect(mongoURI);
        console.log("DB Connected");
    } catch (error) {
        console.error("Error connecting to DB:", error);
        throw error;
    }
};
