/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
}

/* Food Display Section */
.food-display {
  padding: 20px 10px;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.food-display h2 {
  font-size: clamp(20px, 4vw, 28px);
  margin-bottom: 30px;
  color: #333;
  font-weight: 600;
}

/* Food Item List Layout */
.food-display-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  justify-items: center;
  padding: 0 10px;
}

/* Food Item Container */
.Food-item-container {
  width: 100%;
  max-width: 320px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.Food-item-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.Food-item-img-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.Food-item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Food Info Section */
.Food-item-info {
  padding: 10px;
  text-align: left;
}

/* Name and Rating Section */
.Food-item-name-rating {
  display: flex;
  flex-direction: column; /* Stack food name and rating */
  align-items: flex-start; /* Align text to the left */
  margin-bottom: 10px;
}

.Food-item-rating {
  margin-right: auto; /* Push rating to the right */
  display: flex;
  align-items: center; /* Center the rating vertically */
}

/* Food Name */
.Food-item-name {
  font-weight: bold;
  font-size: 22px;
  color: #000; /* Set color to black to ensure visibility */
  display: block;
  margin-bottom: 5px; /* Space between name and rating */
  white-space: nowrap; /* Prevent name from wrapping */
  overflow: hidden; /* Prevent overflow */
  text-overflow: ellipsis; /* Show '...' if name is too long */
}

/* Food Description */
.Food-item-desc {
  font-size: 14px;
  color: #777;
  margin-bottom: 10px;
}

/* Price Per Plate */
.Food-item-price-per-plate {
  font-size: 20px;
  color: #070606;
  font-weight: bold;
  margin-bottom: 5px;
}

/* Total Price */
/* .Food-item-price {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-top: 10px;
} */

/* Cart Buttons */
.Food-item-cart-actions {
  display: flex;
  justify-content: space-between; /* Space between buttons */
  margin-top: 10px;
}

.btn {
  padding: 5px 15px; /* Reduced padding for smaller button size */
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold; /* Make button text bold */
  color: black; /* Set button text color to black */
  transition: background-color 0.3s ease;
}

.btn-primary {
  background-color: #d1a0c5; /* Blue background for Add to Cart */
}

.btn-primary:hover {
  background-color: #b6a0d1; /* Darker blue on hover */
}

.btn-danger {
  background-color: #dc3545; /* Red background for Remove from Cart */
}

.btn-danger:hover {
  background-color: #c82333; /* Darker red on hover */
}

/* Media Queries for Responsive Design */

/* Large Screens */
@media (min-width: 1200px) {
  .food-display-list {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }
}

/* Medium Screens (Tablets) */
@media (max-width: 1024px) {
  .food-display {
    padding: 15px 8px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 18px;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-name {
    font-size: 18px;
  }

  .Food-item-desc {
    font-size: 13px;
  }
}

/* Small Screens (Mobile) */
@media (max-width: 768px) {
  .food-display {
    padding: 15px 5px;
  }

  .food-display h2 {
    margin-bottom: 20px;
    font-size: 22px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    padding: 0 5px;
  }

  .Food-item-container {
    max-width: 100%;
    border-radius: 10px;
  }

  .Food-item-img-container {
    height: 200px;
  }

  .Food-item-info {
    padding: 15px;
  }

  .Food-item-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .Food-item-desc {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 8px;
  }

  .Food-item-cart-actions {
    gap: 10px;
    margin-top: 15px;
  }
}

/* Extra Small Screens */
@media (max-width: 480px) {
  .food-display {
    padding: 10px 3px;
  }

  .food-display-list {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 3px;
  }

  .Food-item-container {
    margin: 0 auto;
    max-width: 95%;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-info {
    padding: 12px;
  }

  .Food-item-name {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .Food-item-desc {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .Food-item-price-per-plate {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 14px;
    min-height: 40px;
  }

  .Food-item-cart-actions {
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
  }

  .Food-item-cart-actions .btn {
    width: 100%;
  }
}

/* Very Small Screens */
@media (max-width: 360px) {
  .food-display {
    padding: 8px 2px;
  }

  .Food-item-container {
    max-width: 98%;
  }

  .Food-item-img-container {
    height: 160px;
  }

  .Food-item-info {
    padding: 10px;
  }

  .Food-item-name {
    font-size: 15px;
  }

  .Food-item-desc {
    font-size: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 15px;
  }
}
