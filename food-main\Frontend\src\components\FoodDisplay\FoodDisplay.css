/* Modern Food Display Styles */
.modern-food-display {
  padding: 40px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* Modern Hero Section */
.food-display-hero {
  position: relative;
  padding: 80px 40px;
  margin-bottom: 60px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-radius: 30px;
  color: white;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(44, 62, 80, 0.3);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.hero-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 2px, transparent 2px);
  background-size: 50px 50px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-badge-icon {
  font-size: 16px;
}

.hero-title {
  font-size: clamp(36px, 6vw, 56px);
  font-weight: 800;
  margin: 0 0 25px 0;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-description {
  font-size: clamp(16px, 2.5vw, 22px);
  line-height: 1.6;
  opacity: 0.9;
  margin: 0 0 50px 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
  max-width: 900px;
  margin: 0 auto;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 25px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.metric-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.metric-info {
  display: flex;
  flex-direction: column;
}

.metric-number {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.veg-metric:hover {
  background: rgba(39, 174, 96, 0.2);
}

.non-veg-metric:hover {
  background: rgba(231, 76, 60, 0.2);
}

.rating-metric:hover {
  background: rgba(241, 196, 15, 0.2);
}

/* Modern Control Panel */
.modern-control-panel {
  background: white;
  border-radius: 25px;
  padding: 40px;
  margin-bottom: 50px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-header {
  text-align: center;
  margin-bottom: 40px;
}

.control-title {
  font-size: clamp(24px, 4vw, 32px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.control-subtitle {
  font-size: clamp(14px, 2vw, 18px);
  color: #7f8c8d;
  margin: 0;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: start;
}

/* Advanced Search Section */
.advanced-search-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

.search-label-icon {
  font-size: 18px;
}

.advanced-search-box {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-input-container {
  position: relative;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.search-input-container:focus-within {
  border-color: #667eea;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.search-prefix-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #7f8c8d;
  z-index: 2;
}

.advanced-search-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 18px 60px 18px 55px;
  font-size: 16px;
  color: #2c3e50;
  background: transparent;
  font-weight: 500;
}

.advanced-search-input::placeholder {
  color: #bdc3c7;
  font-weight: 400;
}

.advanced-clear-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: #e74c3c;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.advanced-clear-btn:hover {
  background: #c0392b;
  transform: translateY(-50%) scale(1.1);
}

.search-suggestions-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.suggestion-label {
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.suggestion-chip {
  background: white;
  border: 1px solid #e9ecef;
  color: #2c3e50;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.suggestion-chip:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-1px);
}

/* Smart Filters Section */
.smart-filters-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

.filter-label-icon {
  font-size: 18px;
}

.smart-filter-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.smart-filter-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
}

.smart-filter-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.smart-filter-card:hover::before {
  left: 100%;
}

.smart-filter-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.smart-filter-card.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.smart-filter-card.veg-card.active {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: #27ae60;
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.smart-filter-card.non-veg-card.active {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: #e74c3c;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.filter-card-icon {
  font-size: 28px;
  flex-shrink: 0;
}

.filter-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-card-title {
  font-size: 18px;
  font-weight: 700;
  color: inherit;
}

.filter-card-count {
  font-size: 14px;
  opacity: 0.7;
  color: inherit;
}

.filter-card-check {
  font-size: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.smart-filter-card.active .filter-card-check {
  opacity: 1;
}
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  text-align: center;
  font-weight: 500;
}

.stat-item.veg {
  border-color: rgba(39, 174, 96, 0.5);
}

.stat-item.non-veg {
  border-color: rgba(231, 76, 60, 0.5);
}

/* Menu Controls */
.menu-controls {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Search Container */
.search-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 50px;
  border: 2px solid #e0e6ed;
  border-radius: 25px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #7f8c8d;
  pointer-events: none;
}

.clear-search {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background: #c0392b;
  transform: translateY(-50%) scale(1.1);
}

/* Food Type Filter */
.food-type-filter {
  text-align: center;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.filter-btn {
  padding: 10px 18px;
  border: 2px solid #e0e6ed;
  background: white;
  color: #7f8c8d;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
}

/* Filter Button Count Badge */
.filter-btn .count-badge {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 700;
  margin-left: 4px;
  transition: all 0.3s ease;
}

.filter-btn.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #bdc3c7;
}

.filter-btn.active {
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  transform: translateY(-1px);
}

.veg-btn {
  border-color: #27ae60;
  color: #27ae60;
  background: rgba(39, 174, 96, 0.05);
}

.veg-btn:hover {
  border-color: #2ecc71;
  color: #2ecc71;
  background: rgba(39, 174, 96, 0.1);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.veg-btn.active {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: #27ae60;
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.non-veg-btn {
  border-color: #e74c3c;
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.05);
}

.non-veg-btn:hover {
  border-color: #c0392b;
  color: #c0392b;
  background: rgba(231, 76, 60, 0.1);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.non-veg-btn.active {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: #e74c3c;
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* Results Info */
.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.results-text {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.clear-filters-btn {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clear-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-items-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px dashed #e0e6ed;
}

.no-items-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-items-container h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.no-items-container p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 30px 0;
  max-width: 400px;
  line-height: 1.6;
}

.reset-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Food Item List Layout */
.food-display-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  justify-items: center;
  padding: 0 10px;
}

/* Food Item Container */
.Food-item-container {
  width: 100%;
  max-width: 320px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.Food-item-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.Food-item-img-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

/* Food Type Indicator */
.food-type-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 6px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 5;
}

.food-type-indicator.veg {
  border-left: 3px solid #27ae60;
  color: #27ae60;
}

.food-type-indicator.non-veg {
  border-left: 3px solid #e74c3c;
  color: #e74c3c;
}

.food-type-icon {
  font-size: 14px;
}

.food-type-text {
  font-size: 10px;
}

.Food-item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Food Info Section */
.Food-item-info {
  padding: 10px;
  text-align: left;
}

/* Name and Rating Section */
.Food-item-name-rating {
  display: flex;
  flex-direction: column; /* Stack food name and rating */
  align-items: flex-start; /* Align text to the left */
  margin-bottom: 10px;
}

.Food-item-rating {
  margin-right: auto; /* Push rating to the right */
  display: flex;
  align-items: center; /* Center the rating vertically */
}

/* Food Name */
.Food-item-name {
  font-weight: bold;
  font-size: 22px;
  color: #000; /* Set color to black to ensure visibility */
  display: block;
  margin-bottom: 5px; /* Space between name and rating */
  white-space: nowrap; /* Prevent name from wrapping */
  overflow: hidden; /* Prevent overflow */
  text-overflow: ellipsis; /* Show '...' if name is too long */
}

/* Food Description */
.Food-item-desc {
  font-size: 14px;
  color: #777;
  margin-bottom: 10px;
}

/* Price Per Plate */
.Food-item-price-per-plate {
  font-size: 20px;
  color: #070606;
  font-weight: bold;
  margin-bottom: 5px;
}

/* Total Price */
/* .Food-item-price {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-top: 10px;
} */

/* Cart Buttons */
.Food-item-cart-actions {
  display: flex;
  justify-content: space-between; /* Space between buttons */
  margin-top: 10px;
}

.btn {
  padding: 5px 15px; /* Reduced padding for smaller button size */
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold; /* Make button text bold */
  color: black; /* Set button text color to black */
  transition: background-color 0.3s ease;
}

.btn-primary {
  background-color: #d1a0c5; /* Blue background for Add to Cart */
}

.btn-primary:hover {
  background-color: #b6a0d1; /* Darker blue on hover */
}

.btn-danger {
  background-color: #dc3545; /* Red background for Remove from Cart */
}

.btn-danger:hover {
  background-color: #c82333; /* Darker red on hover */
}

/* Media Queries for Responsive Design */

/* Large Screens */
@media (min-width: 1200px) {
  .food-display-list {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }
}

/* Medium Screens (Tablets) */
@media (max-width: 1024px) {
  .food-display {
    padding: 15px 8px;
  }

  .food-display-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 25px;
  }

  .food-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .stat-item {
    min-width: 70px;
    padding: 12px 16px;
  }

  .menu-controls {
    padding: 20px;
  }

  .filter-buttons {
    gap: 10px;
  }

  .filter-btn {
    min-width: 85px;
    padding: 8px 14px;
    font-size: 12px;
    border-radius: 18px;
    gap: 4px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 18px;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-name {
    font-size: 18px;
  }

  .Food-item-desc {
    font-size: 13px;
  }
}

/* Small Screens (Mobile) */
@media (max-width: 768px) {
  .food-display {
    padding: 15px 5px;
    margin: 20px 0;
  }

  .food-display-header {
    flex-direction: column;
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-content h2 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .food-stats {
    gap: 10px;
  }

  .stat-item {
    min-width: 60px;
    padding: 10px 12px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 11px;
  }

  .menu-controls {
    padding: 15px;
    margin-bottom: 20px;
  }

  .search-input {
    padding: 12px 40px 12px 40px;
    font-size: 14px;
  }

  .search-icon {
    left: 15px;
    font-size: 16px;
  }

  .filter-buttons {
    gap: 8px;
  }

  .filter-btn {
    min-width: 75px;
    padding: 6px 10px;
    font-size: 11px;
    border-radius: 15px;
    gap: 3px;
  }

  .results-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 12px 15px;
  }

  .results-text {
    font-size: 13px;
  }

  .clear-filters-btn {
    font-size: 11px;
    padding: 6px 12px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    padding: 0 5px;
  }

  .Food-item-container {
    max-width: 100%;
    border-radius: 10px;
  }

  .Food-item-img-container {
    height: 200px;
  }

  .Food-item-info {
    padding: 15px;
  }

  .Food-item-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .Food-item-desc {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 8px;
  }

  .Food-item-cart-actions {
    gap: 10px;
    margin-top: 15px;
  }
}

/* Extra Small Screens */
@media (max-width: 480px) {
  .food-display {
    padding: 10px 3px;
  }

  .food-display-list {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 3px;
  }

  .Food-item-container {
    margin: 0 auto;
    max-width: 95%;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-info {
    padding: 12px;
  }

  .Food-item-name {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .Food-item-desc {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .Food-item-price-per-plate {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 14px;
    min-height: 40px;
  }

  .Food-item-cart-actions {
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
  }

  .Food-item-cart-actions .btn {
    width: 100%;
  }
}

/* Very Small Screens */
@media (max-width: 360px) {
  .food-display {
    padding: 8px 2px;
  }

  .Food-item-container {
    max-width: 98%;
  }

  .Food-item-img-container {
    height: 160px;
  }

  .Food-item-info {
    padding: 10px;
  }

  .Food-item-name {
    font-size: 15px;
  }

  .Food-item-desc {
    font-size: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 15px;
  }
}
