/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
}

/* Food Display Section */
.food-display {
  margin: 40px 0;
  padding: 0 20px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Header Section */
.food-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-content h2 {
  font-size: max(2.5vw, 28px);
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-content p {
  font-size: max(1vw, 16px);
  margin: 0;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Food Stats */
.food-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  text-align: center;
  font-weight: 500;
}

.stat-item.veg {
  border-color: rgba(39, 174, 96, 0.5);
}

.stat-item.non-veg {
  border-color: rgba(231, 76, 60, 0.5);
}

/* Menu Controls */
.menu-controls {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Search Container */
.search-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 50px;
  border: 2px solid #e0e6ed;
  border-radius: 25px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #7f8c8d;
  pointer-events: none;
}

.clear-search {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background: #c0392b;
  transform: translateY(-50%) scale(1.1);
}

/* Food Type Filter */
.food-type-filter {
  text-align: center;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.filter-btn {
  padding: 10px 18px;
  border: 2px solid #e0e6ed;
  background: white;
  color: #7f8c8d;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
}

/* Filter Button Count Badge */
.filter-btn .count-badge {
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 700;
  margin-left: 4px;
  transition: all 0.3s ease;
}

.filter-btn.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #bdc3c7;
}

.filter-btn.active {
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  transform: translateY(-1px);
}

.veg-btn {
  border-color: #27ae60;
  color: #27ae60;
  background: rgba(39, 174, 96, 0.05);
}

.veg-btn:hover {
  border-color: #2ecc71;
  color: #2ecc71;
  background: rgba(39, 174, 96, 0.1);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.veg-btn.active {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: #27ae60;
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.non-veg-btn {
  border-color: #e74c3c;
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.05);
}

.non-veg-btn:hover {
  border-color: #c0392b;
  color: #c0392b;
  background: rgba(231, 76, 60, 0.1);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.non-veg-btn.active {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: #e74c3c;
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* Results Info */
.results-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.results-text {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.clear-filters-btn {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clear-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-items-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px dashed #e0e6ed;
}

.no-items-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-items-container h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.no-items-container p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 30px 0;
  max-width: 400px;
  line-height: 1.6;
}

.reset-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Food Item List Layout */
.food-display-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  justify-items: center;
  padding: 0 10px;
}

/* Food Item Container */
.Food-item-container {
  width: 100%;
  max-width: 320px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.Food-item-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.Food-item-img-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

/* Food Type Indicator */
.food-type-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  padding: 6px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 5;
}

.food-type-indicator.veg {
  border-left: 3px solid #27ae60;
  color: #27ae60;
}

.food-type-indicator.non-veg {
  border-left: 3px solid #e74c3c;
  color: #e74c3c;
}

.food-type-icon {
  font-size: 14px;
}

.food-type-text {
  font-size: 10px;
}

.Food-item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Food Info Section */
.Food-item-info {
  padding: 10px;
  text-align: left;
}

/* Name and Rating Section */
.Food-item-name-rating {
  display: flex;
  flex-direction: column; /* Stack food name and rating */
  align-items: flex-start; /* Align text to the left */
  margin-bottom: 10px;
}

.Food-item-rating {
  margin-right: auto; /* Push rating to the right */
  display: flex;
  align-items: center; /* Center the rating vertically */
}

/* Food Name */
.Food-item-name {
  font-weight: bold;
  font-size: 22px;
  color: #000; /* Set color to black to ensure visibility */
  display: block;
  margin-bottom: 5px; /* Space between name and rating */
  white-space: nowrap; /* Prevent name from wrapping */
  overflow: hidden; /* Prevent overflow */
  text-overflow: ellipsis; /* Show '...' if name is too long */
}

/* Food Description */
.Food-item-desc {
  font-size: 14px;
  color: #777;
  margin-bottom: 10px;
}

/* Price Per Plate */
.Food-item-price-per-plate {
  font-size: 20px;
  color: #070606;
  font-weight: bold;
  margin-bottom: 5px;
}

/* Total Price */
/* .Food-item-price {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-top: 10px;
} */

/* Cart Buttons */
.Food-item-cart-actions {
  display: flex;
  justify-content: space-between; /* Space between buttons */
  margin-top: 10px;
}

.btn {
  padding: 5px 15px; /* Reduced padding for smaller button size */
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold; /* Make button text bold */
  color: black; /* Set button text color to black */
  transition: background-color 0.3s ease;
}

.btn-primary {
  background-color: #d1a0c5; /* Blue background for Add to Cart */
}

.btn-primary:hover {
  background-color: #b6a0d1; /* Darker blue on hover */
}

.btn-danger {
  background-color: #dc3545; /* Red background for Remove from Cart */
}

.btn-danger:hover {
  background-color: #c82333; /* Darker red on hover */
}

/* Media Queries for Responsive Design */

/* Large Screens */
@media (min-width: 1200px) {
  .food-display-list {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }
}

/* Medium Screens (Tablets) */
@media (max-width: 1024px) {
  .food-display {
    padding: 15px 8px;
  }

  .food-display-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 25px;
  }

  .food-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .stat-item {
    min-width: 70px;
    padding: 12px 16px;
  }

  .menu-controls {
    padding: 20px;
  }

  .filter-buttons {
    gap: 10px;
  }

  .filter-btn {
    min-width: 85px;
    padding: 8px 14px;
    font-size: 12px;
    border-radius: 18px;
    gap: 4px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 18px;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-name {
    font-size: 18px;
  }

  .Food-item-desc {
    font-size: 13px;
  }
}

/* Small Screens (Mobile) */
@media (max-width: 768px) {
  .food-display {
    padding: 15px 5px;
    margin: 20px 0;
  }

  .food-display-header {
    flex-direction: column;
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-content h2 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .food-stats {
    gap: 10px;
  }

  .stat-item {
    min-width: 60px;
    padding: 10px 12px;
  }

  .stat-number {
    font-size: 20px;
  }

  .stat-label {
    font-size: 11px;
  }

  .menu-controls {
    padding: 15px;
    margin-bottom: 20px;
  }

  .search-input {
    padding: 12px 40px 12px 40px;
    font-size: 14px;
  }

  .search-icon {
    left: 15px;
    font-size: 16px;
  }

  .filter-buttons {
    gap: 8px;
  }

  .filter-btn {
    min-width: 75px;
    padding: 6px 10px;
    font-size: 11px;
    border-radius: 15px;
    gap: 3px;
  }

  .results-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 12px 15px;
  }

  .results-text {
    font-size: 13px;
  }

  .clear-filters-btn {
    font-size: 11px;
    padding: 6px 12px;
  }

  .food-display-list {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    padding: 0 5px;
  }

  .Food-item-container {
    max-width: 100%;
    border-radius: 10px;
  }

  .Food-item-img-container {
    height: 200px;
  }

  .Food-item-info {
    padding: 15px;
  }

  .Food-item-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .Food-item-desc {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 44px;
    border-radius: 8px;
  }

  .Food-item-cart-actions {
    gap: 10px;
    margin-top: 15px;
  }
}

/* Extra Small Screens */
@media (max-width: 480px) {
  .food-display {
    padding: 10px 3px;
  }

  .food-display-list {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 3px;
  }

  .Food-item-container {
    margin: 0 auto;
    max-width: 95%;
  }

  .Food-item-img-container {
    height: 180px;
  }

  .Food-item-info {
    padding: 12px;
  }

  .Food-item-name {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .Food-item-desc {
    font-size: 13px;
    margin-bottom: 10px;
  }

  .Food-item-price-per-plate {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 14px;
    min-height: 40px;
  }

  .Food-item-cart-actions {
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
  }

  .Food-item-cart-actions .btn {
    width: 100%;
  }
}

/* Very Small Screens */
@media (max-width: 360px) {
  .food-display {
    padding: 8px 2px;
  }

  .Food-item-container {
    max-width: 98%;
  }

  .Food-item-img-container {
    height: 160px;
  }

  .Food-item-info {
    padding: 10px;
  }

  .Food-item-name {
    font-size: 15px;
  }

  .Food-item-desc {
    font-size: 12px;
  }

  .Food-item-price-per-plate {
    font-size: 15px;
  }
}
