# Database Configuration
MONGODB_URI=mongodb://localhost:27017/food-del
# For production, use: mongodb+srv://username:<EMAIL>/food-del

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# Server Configuration
PORT=8889

# Payment Gateway Configuration (Optional)
RAZORPAY_KEY=your-razorpay-key-id
RAZORPAY_SECRET=your-razorpay-secret-key

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=5MB
UPLOAD_PATH=uploads/

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ADMIN_URL=http://localhost:5174
