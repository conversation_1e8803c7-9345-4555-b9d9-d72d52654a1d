/* Menu Page Styles */
.menu-page {
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed navbar */
}

/* <PERSON>u Header */
.menu-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  margin-bottom: 60px;
}

.menu-hero {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.menu-hero-content h1 {
  font-size: max(3.5vw, 42px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.menu-hero-content p {
  font-size: max(1.2vw, 18px);
  margin: 0 0 40px 0;
  opacity: 0.9;
  line-height: 1.6;
}

.menu-stats {
  display: flex;
  gap: 30px;
}

.stat-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.menu-hero-image {
  text-align: center;
}

.menu-hero-image img {
  width: 100%;
  max-width: 500px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Menu Content */
.menu-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Menu Features */
.menu-features {
  background: #f8f9fa;
  padding: 80px 0;
  margin: 80px 0;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.features-container h2 {
  font-size: max(2.5vw, 32px);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 50px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 22px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
}

/* Special Offers */
.special-offers {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 80px 0;
}

.offers-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.offers-container h2 {
  font-size: max(2.5vw, 32px);
  font-weight: 700;
  margin: 0 0 50px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.offers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.offer-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px 30px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s ease;
}

.offer-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.offer-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.offer-card h3 {
  font-size: 24px;
  font-weight: 700;
  margin: 20px 0 15px 0;
}

.offer-card p {
  opacity: 0.9;
  line-height: 1.6;
  margin: 0 0 25px 0;
}

.offer-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.offer-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .menu-hero {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .menu-stats {
    justify-content: center;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .offers-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .menu-page {
    padding-top: 60px;
  }

  .menu-header {
    padding: 60px 0;
    margin-bottom: 40px;
  }

  .menu-hero {
    padding: 0 15px;
  }

  .menu-hero-content h1 {
    font-size: 32px;
  }

  .menu-hero-content p {
    font-size: 16px;
  }

  .menu-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-number {
    font-size: 24px;
  }

  .features-container h2,
  .offers-container h2 {
    font-size: 28px;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .offer-card {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .menu-hero {
    padding: 0 10px;
  }

  .menu-hero-content h1 {
    font-size: 28px;
  }

  .menu-hero-content p {
    font-size: 14px;
  }

  .features-container,
  .offers-container {
    padding: 0 15px;
  }

  .features-container h2,
  .offers-container h2 {
    font-size: 24px;
  }

  .feature-card,
  .offer-card {
    padding: 25px 15px;
  }

  .offer-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}
