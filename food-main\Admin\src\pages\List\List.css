/* List.css */
.list-container {
  padding: 20px 40px 20px 20px; /* Increased padding on the right */
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  margin: 0 auto;
}
.add-img-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Align to the left */
  margin-bottom: 0;
  padding-bottom: 0;
  margin-right: 20px; /* Add 20px margin to the right */
}

.add-img-upload img {
  width: 100%;
  max-width: 150px; /* Keep the reduced image size */
  height: auto;
  border: 2px dashed #ccc;
  border-radius: 5px;
  margin-bottom: 4px;
  cursor: pointer;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-align: center;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-table th,
.product-table td {
  padding: 15px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  vertical-align: middle;
}

.product-table th {
  background-color: #4CAF50;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.product-table td {
  background-color: #fff;
}

.product-table tr:hover {
  background-color: #f8f9fa;
}

.product-table img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #ddd;
}

/* Ensure proper column widths */
.product-table th:nth-child(1),
.product-table td:nth-child(1) {
  width: 25%;
}

.product-table th:nth-child(2),
.product-table td:nth-child(2) {
  width: 15%;
}

.product-table th:nth-child(3),
.product-table td:nth-child(3) {
  width: 15%;
}

.product-table th:nth-child(4),
.product-table td:nth-child(4) {
  width: 25%;
  text-align: center;
}

.product-table th:nth-child(5),
.product-table td:nth-child(5) {
  width: 20%;
  text-align: center;
}

button {
  padding: 8px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #45a049;
}

/* Loading state */
.loading {
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #666;
}

/* Error state */
.error {
  text-align: center;
  padding: 40px;
  color: #d32f2f;
}

.error p {
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  background-color: #f44336;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #d32f2f;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  border: 2px dashed #ddd;
}

.empty-state p {
  margin-bottom: 20px;
  font-size: 16px;
  color: #666;
}

.refresh-btn {
  background-color: #2196F3;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover {
  background-color: #1976D2;
}

/* Remove button specific styling */
.btn-remove {
  background-color: #f44336;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.btn-remove:hover {
  background-color: #d32f2f;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .list-container {
    padding: 15px 10px;
    margin: 10px;
  }

  .product-table {
    font-size: 12px;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .product-table thead,
  .product-table tbody,
  .product-table th,
  .product-table td,
  .product-table tr {
    display: block;
  }

  .product-table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .product-table tr {
    border: 1px solid #ccc;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .product-table td {
    border: none;
    position: relative;
    padding: 8px 8px 8px 50%;
    text-align: left;
    white-space: normal;
  }

  .product-table td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: bold;
    color: #333;
  }

  .product-table img {
    width: 50px;
    height: 50px;
  }

  .btn-remove {
    padding: 8px 12px;
    font-size: 12px;
    margin-top: 5px;
  }
}

@media (max-width: 480px) {
  .list-container {
    padding: 10px 5px;
    margin: 5px;
  }

  .product-table td {
    padding: 6px 6px 6px 45%;
    font-size: 11px;
  }

  .product-table td:before {
    font-size: 11px;
    width: 40%;
  }

  .product-table img {
    width: 40px;
    height: 40px;
  }

  .product-table th, .product-table td {
    padding: 8px 5px;
  }

  button {
    padding: 6px 10px;
    font-size: 12px;
  }

  /* Make the table scrollable horizontally on small screens */
  .product-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}