/* List.css */
.list-container {
  padding: 20px 40px 20px 20px; /* Increased padding on the right */
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  margin: 0 auto;
}
.add-img-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Align to the left */
  margin-bottom: 0;
  padding-bottom: 0;
  margin-right: 20px; /* Add 20px margin to the right */
}

.add-img-upload img {
  width: 100%;
  max-width: 150px; /* Keep the reduced image size */
  height: auto;
  border: 2px dashed #ccc;
  border-radius: 5px;
  margin-bottom: 4px;
  cursor: pointer;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-align: center;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
}

.product-table th,
.product-table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  font-size: 14px;
}

.product-table th {
  background-color: #4CAF50;
  color: white;
}

.product-table td {
  background-color: #fff;
}

.product-table tr:hover {
  background-color: #f1f1f1;
}

button {
  padding: 8px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #45a049;
}

@media (max-width: 600px) {
  .list-container {
    padding: 10px 20px 10px 10px; /* Reduced padding on smaller screens but still larger on the right */
  }

  .product-table {
    font-size: 12px;
  }

  .product-table th, .product-table td {
    padding: 8px 5px;
  }

  button {
    padding: 6px 10px;
    font-size: 12px;
  }

  /* Make the table scrollable horizontally on small screens */
  .product-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}