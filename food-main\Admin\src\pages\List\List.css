/* Modern List Container */
.list-container {
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 5px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  color: #7f8c8d;
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.items-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  min-width: 80px;
}

.items-count .count {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.items-count .label {
  font-size: 12px;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Search and Filter Section */
.search-filter-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Search Container */
.search-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 50px;
  border: 2px solid #e0e6ed;
  border-radius: 25px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #7f8c8d;
  pointer-events: none;
  width: 18px;
  height: 18px;
}

.search-icon::before {
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #7f8c8d;
  border-radius: 50%;
  top: 0;
  left: 0;
}

.search-icon::after {
  content: "";
  position: absolute;
  width: 2px;
  height: 6px;
  background: #7f8c8d;
  transform: rotate(45deg);
  top: 10px;
  left: 10px;
}

.clear-search {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background: #c0392b;
  transform: translateY(-50%) scale(1.1);
}

/* Category Filter */
.category-filter {
  text-align: center;
}

.filter-label {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.category-btn {
  padding: 10px 20px;
  border: 2px solid #e0e6ed;
  background: white;
  color: #7f8c8d;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
  min-width: 80px;
}

.category-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.category-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.category-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  margin-left: 5px;
  font-weight: 700;
}

.category-btn:not(.active) .category-count {
  background: #667eea;
  color: white;
}

/* Search Results Info */
.search-results-info {
  text-align: center;
  margin-top: 15px;
  padding: 10px 15px;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.results-text {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

/* No Results State */
.no-results-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 2px dashed #e0e6ed;
}

.no-results-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-results-state h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.no-results-state p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 30px 0;
  max-width: 400px;
  line-height: 1.6;
}

.clear-filters-btn {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.clear-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}
/* Food Grid Layout */
.food-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

/* Food Card Design */
.food-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.food-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Food Image Section */
.food-image {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  border-radius: 0;
}

.food-card:hover .food-image img {
  transform: scale(1.08);
  filter: brightness(1.1);
}

/* Image Loading State */
.food-image img[src=""],
.food-image img:not([src]) {
  display: none;
}

.food-image::before {
  content: "🍽️";
  font-size: 40px;
  color: #bdc3c7;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.food-image img {
  position: relative;
  z-index: 2;
}

/* Image Error Handling */
.food-image img[alt]:after {
  content: "🍽️ Image not available";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  color: #7f8c8d;
}

.category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Food Content Section */
.food-content {
  padding: 25px;
}

.food-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.food-name {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  line-height: 1.2;
  flex: 1;
  margin-right: 15px;
}

.food-price {
  font-size: 24px;
  font-weight: 800;
  color: #27ae60;
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.food-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Food Meta Information */
.food-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.meta-value {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
}

/* Food Actions */
.food-actions {
  display: flex;
  gap: 12px;
}

.btn-edit,
.btn-remove {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-edit {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-edit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-remove {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-remove:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* Refresh Button */
.refresh-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 60px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.error-state h3 {
  color: #e74c3c;
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.error-state p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 30px 0;
  max-width: 400px;
  line-height: 1.6;
}

.retry-btn {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 2px dashed #e0e6ed;
}

.empty-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-state h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.empty-state p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0 0 30px 0;
  max-width: 400px;
  line-height: 1.6;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .list-container {
    padding: 20px 15px;
  }

  .list-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 20px;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Mobile Search */
  .search-filter-section {
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .search-input-wrapper {
    max-width: 100%;
  }

  .search-input {
    padding: 12px 45px 12px 45px;
    font-size: 16px;
  }

  .search-icon {
    left: 15px;
    font-size: 16px;
  }

  .clear-search {
    right: 12px;
    width: 22px;
    height: 22px;
    font-size: 11px;
  }

  .filter-label {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .category-buttons {
    gap: 8px;
  }

  .category-btn {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 70px;
  }

  .food-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .food-card {
    border-radius: 15px;
  }

  .food-image {
    height: 180px;
  }

  .food-content {
    padding: 20px;
  }

  .food-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .food-name {
    margin-right: 0;
    font-size: 18px;
  }

  .food-price {
    font-size: 20px;
  }

  .food-actions {
    flex-direction: column;
    gap: 10px;
  }

  .btn-edit,
  .btn-remove {
    padding: 14px 16px;
    font-size: 16px;
  }

  .loading-state,
  .error-state,
  .empty-state {
    padding: 60px 20px;
  }

  .empty-icon,
  .error-icon {
    font-size: 60px;
  }
}

@media (max-width: 480px) {
  .list-container {
    padding: 15px 10px;
  }

  .list-header {
    padding: 15px;
  }

  .header-content h2 {
    font-size: 24px;
  }

  .food-grid {
    gap: 15px;
  }

  .food-content {
    padding: 15px;
  }

  .food-name {
    font-size: 16px;
  }

  .food-price {
    font-size: 18px;
  }

  .category-badge {
    font-size: 10px;
    padding: 4px 8px;
  }

  .loading-state,
  .error-state,
  .empty-state {
    padding: 40px 15px;
  }
}

/* Additional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.food-card {
  animation: fadeInUp 0.6s ease forwards;
}

.food-card:nth-child(even) {
  animation-delay: 0.1s;
}

.food-card:nth-child(odd) {
  animation-delay: 0.2s;
}

/* Edit Modal Styles */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.edit-modal {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

.edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid #e0e6ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px 20px 0 0;
}

.edit-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}

.close-modal {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 20px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.edit-modal-content {
  padding: 30px;
}

.edit-item-preview {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 15px;
  border-left: 4px solid #667eea;
}

.edit-item-preview img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 12px;
  border: 2px solid #e0e6ed;
}

.edit-item-details {
  flex: 1;
}

.edit-item-details h4 {
  margin: 0 0 10px 0;
  font-size: 22px;
  color: #2c3e50;
  font-weight: 700;
}

.edit-category {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  margin-bottom: 10px;
}

.edit-price {
  font-size: 20px;
  font-weight: 700;
  color: #27ae60;
  margin: 8px 0;
}

.edit-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  margin: 10px 0 0 0;
}

.edit-actions {
  border-top: 1px solid #e0e6ed;
  padding-top: 25px;
}

.edit-info {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  margin-bottom: 25px;
}

.edit-info p {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.edit-info ul {
  margin: 15px 0 0 20px;
  color: #7f8c8d;
}

.edit-info li {
  margin-bottom: 5px;
}

.edit-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-edit-placeholder,
.btn-cancel {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 140px;
}

.btn-edit-placeholder {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-edit-placeholder:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.btn-cancel {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-cancel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
}

/* Modal Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
  .edit-modal {
    width: 95%;
    max-height: 90vh;
    border-radius: 15px;
  }

  .edit-modal-header {
    padding: 20px 25px;
    border-radius: 15px 15px 0 0;
  }

  .edit-modal-header h3 {
    font-size: 18px;
  }

  .edit-modal-content {
    padding: 25px 20px;
  }

  .edit-item-preview {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .edit-item-preview img {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }

  .edit-buttons {
    flex-direction: column;
  }

  .btn-edit-placeholder,
  .btn-cancel {
    min-width: auto;
    width: 100%;
  }
}

  .product-table th, .product-table td {
    padding: 8px 5px;
  }

  button {
    padding: 6px 10px;
    font-size: 12px;
  }

  /* Make the table scrollable horizontally on small screens */
  .product-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}