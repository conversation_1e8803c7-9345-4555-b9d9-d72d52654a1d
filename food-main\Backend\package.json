{"name": "ecommerce_project", "version": "1.0.0", "description": "This is eCom_project", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js"}, "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "core": "^1.0.113", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.0", "multer": "^1.4.5-lts.1", "razorpay": "^2.9.4", "react-toastify": "^10.0.5", "stripe": "^16.12.0", "validator": "^13.12.0"}, "devDependencies": {"nodemon": "^3.1.7"}}