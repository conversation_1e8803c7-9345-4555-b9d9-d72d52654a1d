.sidebar {
    width: 250px;                          /* Set a fixed width for the sidebar */
    height: 100vh;                         /* Full height of the viewport */
    background-color: #ffffff;             /* White background color for the sidebar */
    color: #333;                           /* Dark text color for contrast */
    display: flex;                         /* Use Flexbox for layout */
    flex-direction: column;                /* Stack items vertically */
    padding: 20px;                         /* Add padding inside the sidebar */
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow effect for depth */
    border: 1px solid #ddd;                /* Border around the sidebar */
    border-radius: 8px;                    /* Optional: rounded corners */
}

.sidebar-option {
    display: flex;
    align-items: center;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 10px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    position: relative;
}

.sidebar-option:hover {
    background-color: #e8f4fd;
    border-color: #007bff;
    transform: translateX(3px);
}

.sidebar-option img {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    transition: transform 0.3s ease;
}

.sidebar-option:hover img {
    transform: scale(1.05);
}

.sidebar-option p {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
}

.sidebar-option.active {
    background-color: #007bff;
    border-color: #0056b3;
    color: white;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.sidebar-option.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #0056b3;
}
/* Media Query for Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 0;
        border: none;
        border-bottom: 1px solid #ddd;
        background-color: #fff;
    }

    .sidebar-option {
        flex-shrink: 0;
        min-width: 120px;
        margin-right: 10px;
        margin-bottom: 0;
        padding: 12px 8px;
        text-align: center;
        font-size: 12px;
        border-radius: 6px;
        flex-direction: column;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
    }

    .sidebar-option:hover {
        background-color: #e9ecef;
        transform: translateY(-2px);
    }

    .sidebar-option.active {
        background-color: #007bff;
        color: white;
        border-color: #0056b3;
    }

    .sidebar-option img {
        width: 20px;
        height: 20px;
        margin-right: 0;
        margin-bottom: 4px;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }

    .sidebar-option p {
        font-size: 11px;
        margin: 0;
        white-space: nowrap;
    }
}

@media (max-width: 480px) {
    .sidebar {
        padding: 8px 5px;
    }

    .sidebar-option {
        min-width: 100px;
        padding: 10px 6px;
        margin-right: 8px;
    }

    .sidebar-option img {
        width: 18px;
        height: 18px;
        margin-bottom: 3px;
    }

    .sidebar-option p {
        font-size: 10px;
    }
}

/* Hide scrollbar for mobile sidebar */
@media (max-width: 768px) {
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    .sidebar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
}
