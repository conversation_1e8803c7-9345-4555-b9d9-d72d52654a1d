.sidebar {
    width: 250px;                          /* Set a fixed width for the sidebar */
    height: 100vh;                         /* Full height of the viewport */
    background-color: #ffffff;             /* White background color for the sidebar */
    color: #333;                           /* Dark text color for contrast */
    display: flex;                         /* Use Flexbox for layout */
    flex-direction: column;                /* Stack items vertically */
    padding: 20px;                         /* Add padding inside the sidebar */
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1); /* Optional shadow effect for depth */
    border: 1px solid #ddd;                /* Border around the sidebar */
    border-radius: 8px;                    /* Optional: rounded corners */
}

.sidebar-option {
    display: flex;                         /* Use Flexbox for sidebar options */
    align-items: center;                   /* Center items vertically */
    padding: 15px;                        /* Padding for each option */
    cursor: pointer;                      /* Change cursor to pointer on hover */
    transition: background-color 0.3s;    /* Smooth background transition */
    background-color: #f9f9f9;            /* Light background for each option */
    border: 1px solid #ddd;                /* Border around each option */
    border-radius: 4px;                    /* Rounded corners for options */
    margin-bottom: 10px;                   /* Space between options */
}

.sidebar-option:hover {
    background-color: #f0f0f0;            /* Change background color on hover */
}

.sidebar-option img {
    width: 24px;                          /* Set a fixed width for icons */
    height: 24px;                         /* Set a fixed height for icons */
    margin-right: 10px;                   /* Space between icon and text */
}

.sidebar-option p {
    margin: 0;                            /* Remove default margin for paragraphs */
    font-size: 16px;                      /* Font size for option text */
}
.sidebar-option.active{
    background-color: #ffffff;
    border-color: red;
}
/* Media Query for Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 0;
        border: none;
        border-bottom: 1px solid #ddd;
        background-color: #fff;
    }

    .sidebar-option {
        flex-shrink: 0;
        min-width: 120px;
        margin-right: 10px;
        margin-bottom: 0;
        padding: 12px 8px;
        text-align: center;
        font-size: 12px;
        border-radius: 6px;
    }

    .sidebar-option img {
        width: 20px;
        height: 20px;
        margin-right: 0;
        margin-bottom: 4px;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }

    .sidebar-option p {
        font-size: 11px;
        margin: 0;
        white-space: nowrap;
    }
}

@media (max-width: 480px) {
    .sidebar {
        padding: 8px 5px;
    }

    .sidebar-option {
        min-width: 100px;
        padding: 10px 6px;
        margin-right: 8px;
    }

    .sidebar-option img {
        width: 18px;
        height: 18px;
        margin-bottom: 3px;
    }

    .sidebar-option p {
        font-size: 10px;
    }
}

/* Hide scrollbar for mobile sidebar */
@media (max-width: 768px) {
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    .sidebar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
}
