/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1001;
    background: #007bff;
    border: none;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: #0056b3;
    transform: scale(1.05);
}

/* Hamburger Icon */
.hamburger {
    display: flex;
    flex-direction: column;
    width: 20px;
    height: 15px;
    position: relative;
}

.hamburger span {
    display: block;
    height: 2px;
    width: 100%;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.hamburger span:nth-child(1) {
    margin-bottom: 4px;
}

.hamburger span:nth-child(2) {
    margin-bottom: 4px;
}

.hamburger.open span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.open span:nth-child(2) {
    opacity: 0;
}

.hamburger.open span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    height: 100vh;
    background-color: #ffffff;
    color: #333;
    display: flex;
    flex-direction: column;
    padding: 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    border-right: 1px solid #e0e0e0;
    position: relative;
    z-index: 1000;
}

.sidebar-content {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sidebar-option {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: transparent;
    border: none;
    border-radius: 0;
    margin: 2px 0;
    text-decoration: none;
    color: #555;
    font-weight: 500;
    position: relative;
    border-left: 4px solid transparent;
}

.sidebar-option:hover {
    background-color: #f8f9fa;
    color: #333;
    border-left-color: #007bff;
}

.sidebar-option img {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    transition: transform 0.3s ease;
    filter: brightness(0.7);
}

.sidebar-option:hover img {
    transform: scale(1.1);
    filter: brightness(0.5);
}

.sidebar-option p {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
}

.sidebar-option.active {
    background-color: #e3f2fd;
    border-left-color: #007bff;
    color: #007bff;
    font-weight: 600;
}

.sidebar-option.active img {
    filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1919%) hue-rotate(213deg) brightness(94%) contrast(101%);
}

/* Mobile Overlay */
.mobile-overlay {
    display: none;
}
/* Mobile Responsive Design */
@media (max-width: 768px) {
    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: block;
    }

    /* Hide sidebar by default on mobile */
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        background-color: #ffffff;
        box-shadow: 2px 0 10px rgba(0,0,0,0.3);
        transition: left 0.3s ease;
        z-index: 1000;
        border-right: 1px solid #e0e0e0;
    }

    /* Show sidebar when mobile menu is open */
    .sidebar.mobile-open {
        left: 0;
    }

    /* Mobile overlay */
    .mobile-overlay {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        animation: fadeIn 0.3s ease;
    }

    /* Sidebar content adjustments */
    .sidebar-content {
        padding: 60px 0 20px 0;
    }

    .sidebar-option {
        padding: 18px 20px;
        margin: 0;
        border-left: 4px solid transparent;
        border-radius: 0;
    }

    .sidebar-option:hover {
        background-color: #f8f9fa;
        border-left-color: #007bff;
    }

    .sidebar-option.active {
        background-color: #e3f2fd;
        border-left-color: #007bff;
        color: #007bff;
    }

    .sidebar-option img {
        width: 24px;
        height: 24px;
        margin-right: 15px;
    }

    .sidebar-option p {
        font-size: 16px;
        font-weight: 500;
    }
}

@media (max-width: 480px) {
    .mobile-menu-toggle {
        top: 10px;
        left: 10px;
        padding: 8px;
    }

    .hamburger {
        width: 18px;
        height: 13px;
    }

    .sidebar {
        width: 220px;
        left: -220px;
    }

    .sidebar.mobile-open {
        left: 0;
    }

    .sidebar-option {
        padding: 16px 18px;
    }

    .sidebar-option img {
        width: 22px;
        height: 22px;
        margin-right: 12px;
    }

    .sidebar-option p {
        font-size: 15px;
    }
}

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@media (max-width: 480px) {
    .sidebar {
        padding: 8px 5px;
    }

    .sidebar-option {
        min-width: 100px;
        padding: 10px 6px;
        margin-right: 8px;
    }

    .sidebar-option img {
        width: 18px;
        height: 18px;
        margin-bottom: 3px;
    }

    .sidebar-option p {
        font-size: 10px;
    }
}

/* Hide scrollbar for mobile sidebar */
@media (max-width: 768px) {
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    .sidebar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
}
