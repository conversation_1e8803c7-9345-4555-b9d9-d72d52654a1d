.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    min-height: 60px;
}

.logo {
    height: 45px;
    max-width: 150px;
    object-fit: contain;
}

.profile {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.profile:hover {
    transform: scale(1.05);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .navbar {
        padding: 10px 60px 10px 15px; /* Add left padding for mobile menu button */
        min-height: 50px;
        position: relative;
        z-index: 998; /* Below mobile menu button but above content */
    }

    .logo {
        height: 35px;
        max-width: 120px;
        margin-left: 10px; /* Add some space from mobile menu button */
    }

    .profile {
        height: 35px;
        width: 35px;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 8px 10px;
        min-height: 45px;
    }

    .logo {
        height: 30px;
        max-width: 100px;
    }

    .profile {
        height: 30px;
        width: 30px;
    }
}
