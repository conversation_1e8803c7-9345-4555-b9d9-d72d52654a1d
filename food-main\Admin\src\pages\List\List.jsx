import axios from "axios";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "./List.css";

const List = () => {
  const listUrl = "http://localhost:8889/api/food/list";
  const removeUrl = "http://localhost:8889/api/food/remove";
  const [list, setList] = useState([]);
  const [filteredList, setFilteredList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  const fetchList = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(listUrl);
      console.log('API Response:', response.data); // Debug log

      if (response.data.success) {
        const items = response.data.data || [];
        setList(items);
        setFilteredList(items);
        if (items.length === 0) {
          toast.info("No food items found. Add some items first!");
        }
      } else {
        setError("Failed to fetch the list");
        toast.error("Failed to fetch the list");
      }
    } catch (error) {
      console.error('Fetch error:', error); // Debug log
      const errorMessage = error.response?.data?.message || error.message;
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Search and Filter Logic
  const handleSearch = (searchValue) => {
    setSearchTerm(searchValue);
    filterItems(searchValue, selectedCategory);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    filterItems(searchTerm, category);
  };

  const filterItems = (search, category) => {
    let filtered = list;

    // Filter by search term
    if (search.trim() !== "") {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(search.toLowerCase()) ||
        item.description.toLowerCase().includes(search.toLowerCase()) ||
        item.category.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Filter by category
    if (category !== "All") {
      filtered = filtered.filter(item => item.category === category);
    }

    setFilteredList(filtered);
  };

  // Get unique categories
  const getCategories = () => {
    const categories = ["All", ...new Set(list.map(item => item.category))];
    return categories;
  };

  // Handle Edit Function
  const handleEdit = (item) => {
    setEditingItem(item);
    setEditModalOpen(true);
  };

  // Close Edit Modal
  const closeEditModal = () => {
    setEditModalOpen(false);
    setEditingItem(null);
  };

  // Handle Edit Save (for future implementation)
  const handleEditSave = async (updatedItem) => {
    try {
      // This would be the API call to update the item
      // const response = await axios.put(`http://localhost:8889/api/food/update/${updatedItem._id}`, updatedItem);

      // For now, just show a message
      toast.info(`Edit functionality for "${updatedItem.name}" will be implemented soon!`);
      closeEditModal();

      // After successful update, refresh the list
      // fetchList();
    } catch (error) {
      toast.error(`Error updating item: ${error.message}`);
    }
  };

  const handleRemove = async (id, itemName) => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      `Are you sure you want to remove "${itemName}"?\n\nThis action cannot be undone.`
    );

    if (!isConfirmed) {
      return; // User cancelled
    }

    try {
      console.log('Removing item with ID:', id); // Debug log
      const response = await axios.delete(`${removeUrl}/${id}`);
      console.log('Remove response:', response.data); // Debug log

      if (response.data.success) {
        toast.success(`"${itemName}" removed successfully!`);

        // Update both list and filteredList
        const updatedList = list.filter((item) => item._id !== id);
        setList(updatedList);

        // Re-apply current filters to the updated list
        const updatedFilteredList = updatedList.filter(item => {
          const matchesSearch = searchTerm === "" ||
            item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.category.toLowerCase().includes(searchTerm.toLowerCase());

          const matchesCategory = selectedCategory === "All" || item.category === selectedCategory;

          return matchesSearch && matchesCategory;
        });

        setFilteredList(updatedFilteredList);
      } else {
        toast.error("Failed to remove food item");
      }
    } catch (error) {
      console.error('Remove error:', error); // Debug log
      toast.error(`Error: ${error.response?.data?.message || error.message}`);
    }
  };

  useEffect(() => {
    fetchList();
  }, []);

  if (loading) {
    return (
      <div className="list-container">
        <div className="list-header">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading delicious food items...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="list-container">
        <div className="list-header">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <h3>Oops! Something went wrong</h3>
          <p>{error}</p>
          <button onClick={fetchList} className="retry-btn">
            <span>🔄</span> Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="list-container">
      <div className="list-header">
        <div className="header-content">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="header-actions">
          <button onClick={fetchList} className="refresh-btn">
            <span>🔄</span> Refresh
          </button>
          <div className="items-count">
            <span className="count">{filteredList.length}</span>
            <span className="label">of {list.length}</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="search-filter-section">
        <div className="search-container">
          <div className="search-input-wrapper">
            <span className="search-icon"></span>
            <input
              type="text"
              placeholder="Search by name, description, or category..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="search-input"
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => handleSearch("")}
              >
                ✕
              </button>
            )}
          </div>
        </div>

        <div className="category-filter">
          <div className="filter-label">Filter by Category:</div>
          <div className="category-buttons">
            {getCategories().map((category) => (
              <button
                key={category}
                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => handleCategoryFilter(category)}
              >
                {category}
                {category !== "All" && (
                  <span className="category-count">
                    {list.filter(item => item.category === category).length}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Search Results Info */}
        {(searchTerm || selectedCategory !== "All") && (
          <div className="search-results-info">
            <span className="results-text">
              {filteredList.length === 0
                ? "No items found"
                : `Showing ${filteredList.length} of ${list.length} items`
              }
              {searchTerm && ` for "${searchTerm}"`}
              {selectedCategory !== "All" && ` in "${selectedCategory}"`}
            </span>
          </div>
        )}
      </div>

      {list.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🍽️</div>
          <h3>No Food Items Yet</h3>
          <p>Start building your menu by adding delicious food items!</p>
          <button onClick={fetchList} className="refresh-btn">
            <span>🔄</span> Refresh List
          </button>
        </div>
      ) : filteredList.length === 0 ? (
        <div className="no-results-state">
          <div className="no-results-icon">🔍</div>
          <h3>No Items Found</h3>
          <p>No items match your search criteria. Try different keywords or categories.</p>
          <button onClick={() => {
            handleSearch("");
            handleCategoryFilter("All");
          }} className="clear-filters-btn">
            <span>🔄</span> Clear Filters
          </button>
        </div>
      ) : (
        <div className="food-grid">
          {filteredList.map((item) => (
            <div key={item._id} className="food-card">
              <div className="food-image">
                <img
                  src={
                    item.image
                      ? `http://localhost:8889/uploads/${item.image}`
                      : "/placeholder.jpg"
                  }
                  alt={item.name}
                  onError={(e) => {
                    e.target.src = "/placeholder.jpg";
                  }}
                />
                <div className="category-badge">{item.category}</div>
              </div>

              <div className="food-content">
                <div className="food-header">
                  <h3 className="food-name">{item.name}</h3>
                  <div className="food-price">₹{item.price}</div>
                </div>

                <p className="food-description">{item.description}</p>

                <div className="food-meta">
                  <div className="meta-item">
                    <span className="meta-label">Category:</span>
                    <span className="meta-value">{item.category}</span>
                  </div>
                  <div className="meta-item">
                    <span className="meta-label">Price:</span>
                    <span className="meta-value">₹{item.price}</span>
                  </div>
                </div>

                <div className="food-actions">
                  <button
                    className="btn-edit"
                    onClick={() => handleEdit(item)}
                    title={`Edit ${item.name}`}
                  >
                    <span>✏️</span> Edit
                  </button>
                  <button
                    className="btn-remove"
                    onClick={() => handleRemove(item._id, item.name)}
                    title={`Remove ${item.name}`}
                  >
                    <span>🗑️</span> Remove
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Edit Modal */}
      {editModalOpen && editingItem && (
        <div className="edit-modal-overlay" onClick={closeEditModal}>
          <div className="edit-modal" onClick={(e) => e.stopPropagation()}>
            <div className="edit-modal-header">
              <h3>📝 Edit Food Item</h3>
              <button className="close-modal" onClick={closeEditModal}>✕</button>
            </div>

            <div className="edit-modal-content">
              <div className="edit-item-preview">
                <img
                  src={editingItem.image ? `http://localhost:8889/uploads/${editingItem.image}` : "/placeholder.jpg"}
                  alt={editingItem.name}
                  onError={(e) => { e.target.src = "/placeholder.jpg"; }}
                />
                <div className="edit-item-details">
                  <h4>{editingItem.name}</h4>
                  <p className="edit-category">{editingItem.category}</p>
                  <p className="edit-price">₹{editingItem.price}</p>
                  <p className="edit-description">{editingItem.description}</p>
                </div>
              </div>

              <div className="edit-actions">
                <div className="edit-info">
                  <p>🚧 <strong>Edit functionality is coming soon!</strong></p>
                  <p>For now, you can:</p>
                  <ul>
                    <li>✅ View item details here</li>
                    <li>🗑️ Remove items using the Remove button</li>
                    <li>➕ Add new items in the Add section</li>
                  </ul>
                </div>

                <div className="edit-buttons">
                  <button
                    className="btn-edit-placeholder"
                    onClick={() => handleEditSave(editingItem)}
                  >
                    🔧 Edit (Coming Soon)
                  </button>
                  <button
                    className="btn-remove"
                    onClick={() => {
                      closeEditModal();
                      handleRemove(editingItem._id, editingItem.name);
                    }}
                  >
                    🗑️ Remove Item
                  </button>
                  <button className="btn-cancel" onClick={closeEditModal}>
                    ❌ Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default List;
