import axios from "axios";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "./List.css";

const List = () => {
  const listUrl = "http://localhost:8889/api/food/list";
  const removeUrl = "http://localhost:8889/api/food/remove";
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchList = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(listUrl);
      console.log('API Response:', response.data); // Debug log

      if (response.data.success) {
        setList(response.data.data || []);
        if (response.data.data.length === 0) {
          toast.info("No food items found. Add some items first!");
        }
      } else {
        setError("Failed to fetch the list");
        toast.error("Failed to fetch the list");
      }
    } catch (error) {
      console.error('Fetch error:', error); // Debug log
      const errorMessage = error.response?.data?.message || error.message;
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async (id) => {
    try {
      const response = await axios.delete(`${removeUrl}/${id}`);
      if (response.data.success) {
        toast.success("Food removed successfully");
        setList(list.filter((item) => item._id !== id));
      } else {
        toast.error("Failed to remove food item");
      }
    } catch (error) {
      toast.error(`Error: ${error.response?.data?.message || error.message}`);
    }
  };

  useEffect(() => {
    fetchList();
  }, []);

  if (loading) {
    return (
      <div className="list-container">
        <div className="list-header">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading delicious food items...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="list-container">
        <div className="list-header">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <h3>Oops! Something went wrong</h3>
          <p>{error}</p>
          <button onClick={fetchList} className="retry-btn">
            <span>🔄</span> Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="list-container">
      <div className="list-header">
        <div className="header-content">
          <h2>🍽️ Food Items List</h2>
          <p>Manage your restaurant menu items</p>
        </div>
        <div className="header-actions">
          <button onClick={fetchList} className="refresh-btn">
            <span>🔄</span> Refresh
          </button>
          <div className="items-count">
            <span className="count">{list.length}</span>
            <span className="label">Items</span>
          </div>
        </div>
      </div>

      {list.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🍽️</div>
          <h3>No Food Items Yet</h3>
          <p>Start building your menu by adding delicious food items!</p>
          <button onClick={fetchList} className="refresh-btn">
            <span>🔄</span> Refresh List
          </button>
        </div>
      ) : (
        <div className="food-grid">
          {list.map((item) => (
            <div key={item._id} className="food-card">
              <div className="food-image">
                <img
                  src={
                    item.image
                      ? `http://localhost:8889/uploads/${item.image}`
                      : "/placeholder.jpg"
                  }
                  alt={item.name}
                  onError={(e) => {
                    e.target.src = "/placeholder.jpg";
                  }}
                />
                <div className="category-badge">{item.category}</div>
              </div>

              <div className="food-content">
                <div className="food-header">
                  <h3 className="food-name">{item.name}</h3>
                  <div className="food-price">₹{item.price}</div>
                </div>

                <p className="food-description">{item.description}</p>

                <div className="food-meta">
                  <div className="meta-item">
                    <span className="meta-label">Category:</span>
                    <span className="meta-value">{item.category}</span>
                  </div>
                  <div className="meta-item">
                    <span className="meta-label">Price:</span>
                    <span className="meta-value">₹{item.price}</span>
                  </div>
                </div>

                <div className="food-actions">
                  <button
                    className="btn-edit"
                    onClick={() => {/* Add edit functionality */}}
                  >
                    <span>✏️</span> Edit
                  </button>
                  <button
                    className="btn-remove"
                    onClick={() => handleRemove(item._id)}
                  >
                    <span>🗑️</span> Remove
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default List;
