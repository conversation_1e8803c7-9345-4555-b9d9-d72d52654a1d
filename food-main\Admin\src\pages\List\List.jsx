import axios from "axios";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "./List.css";

const List = () => {
  const listUrl = "http://localhost:8889/api/food/list";
  const removeUrl = "http://localhost:8889/api/food/remove";
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchList = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get(listUrl);
      console.log('API Response:', response.data); // Debug log

      if (response.data.success) {
        setList(response.data.data || []);
        if (response.data.data.length === 0) {
          toast.info("No food items found. Add some items first!");
        }
      } else {
        setError("Failed to fetch the list");
        toast.error("Failed to fetch the list");
      }
    } catch (error) {
      console.error('Fetch error:', error); // Debug log
      const errorMessage = error.response?.data?.message || error.message;
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async (id) => {
    try {
      const response = await axios.delete(`${removeUrl}/${id}`);
      if (response.data.success) {
        toast.success("Food removed successfully");
        setList(list.filter((item) => item._id !== id));
      } else {
        toast.error("Failed to remove food item");
      }
    } catch (error) {
      toast.error(`Error: ${error.response?.data?.message || error.message}`);
    }
  };

  useEffect(() => {
    fetchList();
  }, []);

  if (loading) {
    return (
      <div className="list-container">
        <h2>Product List</h2>
        <div className="loading">Loading food items...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="list-container">
        <h2>Product List</h2>
        <div className="error">
          <p>Error: {error}</p>
          <button onClick={fetchList} className="retry-btn">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="list-container">
      <h2>Product List</h2>
      {list.length === 0 ? (
        <div className="empty-state">
          <p>No food items found. Add some items first!</p>
          <button onClick={fetchList} className="refresh-btn">
            Refresh
          </button>
        </div>
      ) : (
        <table className="product-table">
          <thead>
            <tr>
              <th>Product Name</th>
              <th>Category</th>
              <th>Price</th>
              <th>Image</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {list.map((item) => (
              <tr key={item._id}>
                <td>{item.name}</td>
                <td>{item.category}</td>
                <td>₹{item.price}</td>
                <td>
                  <img
                    src={
                      item.image
                        ? `http://localhost:8889/uploads/${item.image}`
                        : "/placeholder.jpg"
                    }
                    alt={item.name}
                    width="100"
                    onError={(e) => {
                      e.target.src = "/placeholder.jpg";
                    }}
                  />
                </td>
                <td>
                  <button
                    className="btn-remove"
                    onClick={() => handleRemove(item._id)}
                  >
                    Remove
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default List;
