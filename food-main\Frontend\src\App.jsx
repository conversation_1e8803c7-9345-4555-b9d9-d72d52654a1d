// App.js
import { useState } from "react";
import { Route, Routes } from "react-router-dom";
import FoodDetail from './components/FoodDetail/FoodDetail';
import Footer from "./components/Footer/Footer";
import LoginPopup from "./components/LoginPopup/LoginPopup"; // Fixed filename
import Navbar from "./components/Navbar/Navbar";
import Cart from "./pages/Cart/Cart";
import Home from "./pages/Home/Home";
import MyOrders from "./pages/MyOrders/MyOrders"; // Corrected import statement
import PlaceOrder from "./pages/PlaceOrders/PlaceOrder";
import Verify from "./pages/Verify/Verify"; // Added import for Verify component if required

const App = () => {
  const [showLogin, setShowLogin] = useState(false);

  return (
    <>
      {showLogin && <LoginPopup setShowLogin={setShowLogin} />}{" "}
      {/* Conditional rendering for LoginPopup */}
      <div className="app">
        <Navbar setShowLogin={setShowLogin} />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/cart" element={<Cart />} />
          <Route path="/order" element={<PlaceOrder />} />
          <Route path="/verify" element={<Verify />} />
          <Route path="/myorders" element={<MyOrders />} />
          <Route path="/food/:id" element={<FoodDetail />} />
        </Routes>
      </div>
      <Footer />
    </>
  );
};

export default App;
