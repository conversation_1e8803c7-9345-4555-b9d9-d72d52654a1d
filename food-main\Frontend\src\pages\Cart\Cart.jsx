import axios from "axios";
import { jwtDecode } from "jwt-decode";
import { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { StoreContext } from "../../context/StoreContext";
import "./Cart.css";

const Cart = () => {
  const { cartItems, setCartItems, token } = useContext(StoreContext);
  const [quantity, setQuantity] = useState({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) return;

    const fetchCartData = async () => {
      try {
        const decodedToken = jwtDecode(token);
        if (!decodedToken?.id) return;

        const response = await axios.get(
          `http://localhost:8889/api/cart/getCart/${decodedToken.id}`,
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setCartItems(response.data?.items || []);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching cart data:", error.response?.data?.message || error.message);
        setLoading(false);
      }
    };

    fetchCartData();
  }, [token]);

  if (loading) return <p>Loading cart...</p>;

  const safeCartItems = Array.isArray(cartItems) ? cartItems : [];

  const handleRemoveFromCart = async (itemId) => {
    if (!itemId || !token) return;

    try {
      const userId = jwtDecode(token)?.id;
      await axios.delete(`http://localhost:8889/api/cart/removeItem/${userId}/${itemId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setCartItems((prev) => prev.filter((item) => item.itemId?._id !== itemId));
    } catch (error) {
      console.error("Error removing item:", error.response?.data?.message || error.message);
    }
  };

  // ✅ Function to remove all items from cart
  const handleClearCart = async () => {
    if (!token) return;

    try {
      const userId = jwtDecode(token)?.id;
      await axios.delete(`http://localhost:8889/api/cart/clearCart/${userId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setCartItems([]); // Update UI immediately
    } catch (error) {
      console.error("Error clearing cart:", error.response?.data?.message || error.message);
    }
  };

  const deliveryFee = 10;
  const subtotal = safeCartItems.reduce(
    (total, item) => total + (item.itemId?.price || 0) * (quantity[item.itemId?._id] || item.quantity),
    0
  );
  const totalAmount = subtotal + (safeCartItems.length > 0 ? deliveryFee : 0);

  return (
    <div className="modern-cart">
      <div className="cart-header">
        <h1 className="cart-title">🛒 Your Shopping Cart</h1>
        <p className="cart-subtitle">Review your items and proceed to checkout</p>
      </div>

      {safeCartItems.length === 0 ? (
        <div className="empty-cart">
          <div className="empty-cart-icon">🛒</div>
          <h3>Your cart is empty</h3>
          <p>Looks like you haven't added any items to your cart yet.</p>
          <button
            className="continue-shopping-btn"
            onClick={() => navigate("/menu")}
          >
            🍽️ Continue Shopping
          </button>
        </div>
      ) : (
        <>
          <div className="cart-items-container">
            {safeCartItems.map((item, index) => {
              const itemTotal = (item.itemId?.price || 0) * (quantity[item.itemId?._id] || item.quantity);
              return (
                <div className="cart-item-card" key={item.itemId?._id || `cart-item-${index}`}>
                  <div className="item-image">
                    <img
                      src={`http://localhost:8889/uploads/${item.itemId?.image}`}
                      alt={item.itemId?.name}
                      onError={(e) => {
                        e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop';
                      }}
                    />
                  </div>

                  <div className="item-details">
                    <h3 className="item-name">{item.itemId?.name}</h3>
                    <p className="item-price">₹{item.itemId?.price?.toFixed(2)}</p>
                    <div className="item-description">
                      Fresh and delicious, made with premium ingredients
                    </div>
                  </div>

                  <div className="item-controls">
                    <div className="quantity-control">
                      <label>Quantity</label>
                      <div className="quantity-input-group">
                        <button
                          className="qty-btn"
                          onClick={() => {
                            const currentQty = quantity[item.itemId?._id] || item.quantity;
                            if (currentQty > 1) {
                              setQuantity({ ...quantity, [item.itemId?._id]: currentQty - 1 });
                            }
                          }}
                        >
                          -
                        </button>
                        <input
                          type="number"
                          className="qty-input"
                          value={quantity[item.itemId?._id] || item.quantity}
                          min="1"
                          max="99"
                          onChange={(e) => {
                            const value = Math.max(1, parseInt(e.target.value) || 1);
                            setQuantity({ ...quantity, [item.itemId?._id]: value });
                          }}
                        />
                        <button
                          className="qty-btn"
                          onClick={() => {
                            const currentQty = quantity[item.itemId?._id] || item.quantity;
                            setQuantity({ ...quantity, [item.itemId?._id]: currentQty + 1 });
                          }}
                        >
                          +
                        </button>
                      </div>
                    </div>

                    <div className="item-total">
                      <span className="total-label">Total</span>
                      <span className="total-amount">₹{itemTotal.toFixed(2)}</span>
                    </div>
                  </div>

                  <div className="item-actions">
                    <button
                      className="remove-btn"
                      onClick={() => handleRemoveFromCart(item.itemId?._id)}
                    >
                      🗑️ Remove
                    </button>
                    <button
                      className="order-single-btn"
                      onClick={() =>
                        navigate("/order", {
                          state: {
                            orderItems: [{ ...item, quantity: quantity[item.itemId?._id] || item.quantity }],
                            subtotal: itemTotal,
                            totalAmount: itemTotal + deliveryFee,
                            deliveryFee,
                          },
                        })
                      }
                    >
                      🚀 Order This Item
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="cart-actions">
            <button className="clear-cart-btn" onClick={handleClearCart}>
              🗑️ Clear All Items
            </button>
          </div>
        </>
      )}

      {safeCartItems.length > 0 && (
        <div className="cart-summary-card">
          <div className="summary-header">
            <h3>📋 Order Summary</h3>
            <p className="summary-subtitle">{safeCartItems.length} item{safeCartItems.length > 1 ? 's' : ''} in your cart</p>
          </div>

          <div className="summary-details">
            <div className="summary-row">
              <span className="summary-label">Subtotal</span>
              <span className="summary-value">₹{subtotal.toFixed(2)}</span>
            </div>
            <div className="summary-row">
              <span className="summary-label">Delivery Fee</span>
              <span className="summary-value delivery-fee">
                {safeCartItems.length > 0 ? `₹${deliveryFee.toFixed(2)}` : 'FREE'}
              </span>
            </div>
            <div className="summary-row total-row">
              <span className="summary-label">Total Amount</span>
              <span className="summary-value total-amount">₹{totalAmount.toFixed(2)}</span>
            </div>
          </div>

          <div className="summary-actions">
            <button
              className="checkout-btn"
              onClick={() =>
                navigate("/order", {
                  state: {
                    orderItems: safeCartItems,
                    subtotal,
                    totalAmount,
                    deliveryFee,
                  },
                })
              }
            >
              🚀 Proceed to Checkout
            </button>
            <div className="checkout-note">
              <span className="note-icon">🔒</span>
              <span className="note-text">Secure checkout guaranteed</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;
