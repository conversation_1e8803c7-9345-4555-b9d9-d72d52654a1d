import axios from "axios";
import { jwtDecode } from "jwt-decode";
import { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { StoreContext } from "../../context/StoreContext";
import "./Cart.css";

const Cart = () => {
  const { cartItems, setCartItems, token } = useContext(StoreContext);
  const [quantity, setQuantity] = useState({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) return;

    const fetchCartData = async () => {
      try {
        const decodedToken = jwtDecode(token);
        if (!decodedToken?.id) return;

        const response = await axios.get(
          `http://localhost:8889/api/cart/getCart/${decodedToken.id}`,
          { headers: { Authorization: `Bearer ${token}` } }
        );

        setCartItems(response.data?.items || []);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching cart data:", error.response?.data?.message || error.message);
        setLoading(false);
      }
    };

    fetchCartData();
  }, [token]);

  if (loading) return <p>Loading cart...</p>;

  const safeCartItems = Array.isArray(cartItems) ? cartItems : [];

  const handleRemoveFromCart = async (itemId) => {
    if (!itemId || !token) return;

    try {
      const userId = jwtDecode(token)?.id;
      await axios.delete(`http://localhost:8889/api/cart/removeItem/${userId}/${itemId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setCartItems((prev) => prev.filter((item) => item.itemId?._id !== itemId));
    } catch (error) {
      console.error("Error removing item:", error.response?.data?.message || error.message);
    }
  };

  // ✅ Function to remove all items from cart
  const handleClearCart = async () => {
    if (!token) return;

    try {
      const userId = jwtDecode(token)?.id;
      await axios.delete(`http://localhost:8889/api/cart/clearCart/${userId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setCartItems([]); // Update UI immediately
    } catch (error) {
      console.error("Error clearing cart:", error.response?.data?.message || error.message);
    }
  };

  const deliveryFee = 10;
  const subtotal = safeCartItems.reduce(
    (total, item) => total + (item.itemId?.price || 0) * (quantity[item.itemId?._id] || item.quantity),
    0
  );
  const totalAmount = subtotal + (safeCartItems.length > 0 ? deliveryFee : 0);

  return (
    <div className="cart">
      <h2>Your Cart</h2>
      {safeCartItems.length === 0 ? (
        <p>Your cart is empty.</p>
      ) : (
        <>
          <div className="cart-item">
            <div className="cart-items-title">
              <p>Items</p>
              <p>Title</p>
              <p>Price</p>
              <p>Quantity</p>
              <p>Total</p>
              <p>Remove</p>
              <p>Order</p>
            </div>
            <hr />
            {safeCartItems.map((item, index) => {
              const itemTotal = (item.itemId?.price || 0) * (quantity[item.itemId?._id] || item.quantity);
              return (
                <div className="cart-items-item" key={item.itemId?._id || `cart-item-${index}`}>
                  <img
                    src={`http://localhost:8889/uploads/${item.itemId?.image}`}
                    alt={item.itemId?.name}
                  />
                  <p>{item.itemId?.name}</p>
                  <p>Rs {item.itemId?.price?.toFixed(2)}</p>
                  <input
                    type="number"
                    value={quantity[item.itemId?._id] || item.quantity}
                    min="1"
                    max="99"
                    onChange={(e) => {
                      const value = Math.max(1, parseInt(e.target.value) || 1);
                      setQuantity({ ...quantity, [item.itemId?._id]: value });
                    }}
                  />
                  <p>Rs {itemTotal.toFixed(2)}</p>
                  <button onClick={() => handleRemoveFromCart(item.itemId?._id)}>Remove</button>

                  <button
                    onClick={() =>
                      navigate("/Order", {
                        state: {
                          orderItems: [{ ...item, quantity: quantity[item.itemId?._id] || item.quantity }],
                          subtotal: itemTotal,
                          totalAmount: itemTotal + deliveryFee,
                          deliveryFee,
                        },
                      })
                    }
                  >
                    Place Order
                  </button>
                </div>
              );
            })}
          </div>

          {/* ✅ "Clear Cart" Button */}
          <button className="clear-cart-btn" onClick={handleClearCart}>
            Clear Cart
          </button>
        </>
      )}

      {safeCartItems.length > 0 && (
        <div className="cart-summary">
          <h3>Cart Summary</h3>
          <p>Subtotal: Rs {subtotal.toFixed(2)}</p>
          <p>Delivery Fee: Rs {deliveryFee.toFixed(2)}</p>
          <p><strong>Total: Rs {totalAmount.toFixed(2)}</strong></p>
          <button
            onClick={() =>
              navigate("/Order", {
                state: {
                  orderItems: safeCartItems,
                  subtotal,
                  totalAmount,
                  deliveryFee,
                },
              })
            }
          >
            Order All
          </button>
        </div>
      )}
    </div>
  );
};

export default Cart;
