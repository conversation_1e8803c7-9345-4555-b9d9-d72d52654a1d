.explore-menu {
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 20px 0;
    color: #262626;
    font-weight: 500;
}

.explore-menu h1 {
    font-size: clamp(24px, 4vw, 32px);
    margin-bottom: 10px;
    color: #333;
}

.explore-menu-text {
    max-width: 70%;
    color: #666;
    line-height: 1.6;
    font-size: clamp(14px, 2vw, 16px);
}

.explore-menu-list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 25px;
    text-align: center;
    margin: 25px 0;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
    padding: 10px 0;
}

.explore-menu-list::-webkit-scrollbar {
    display: none;
}

.explore-menu-list img {
    width: 7.5vw;
    min-width: 80px;
    max-width: 120px;
    height: auto;
    cursor: pointer;
    border-radius: 50%;
    transition: 0.2s;
}

.explore-menu-list p {
    margin-top: 10px;
    color: #747474;
    font-size: max(1.4vw, 16px);
    cursor: pointer;
}

/* Featured Items Section */
.featured-items-section {
    margin: 40px 0;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.featured-header {
    text-align: center;
    margin-bottom: 30px;
}

.featured-header h2 {
    font-size: max(2.5vw, 28px);
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.featured-header p {
    color: #7f8c8d;
    font-size: max(1vw, 16px);
    margin: 0 0 20px 0;
}

.toggle-view-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toggle-view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.toggle-icon {
    font-size: 14px;
    transition: transform 0.3s ease;
}

/* Featured Items Grid */
.featured-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.featured-food-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.featured-food-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Food Type Badge */
.food-type-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.95);
    padding: 6px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.food-type-badge.veg {
    border-left: 4px solid #27ae60;
    color: #27ae60;
}

.food-type-badge.non-veg {
    border-left: 4px solid #e74c3c;
    color: #e74c3c;
}

.food-type-icon {
    font-size: 14px;
}

.food-type-text {
    font-size: 10px;
}

/* Food Image */
.featured-food-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.featured-food-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-food-card:hover .featured-food-image img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 20px 15px 15px;
    display: flex;
    justify-content: flex-end;
}

.category-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Food Info */
.featured-food-info {
    padding: 20px;
}

.food-name {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.food-description {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 15px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.food-price {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 15px;
}

/* Food Actions */
.featured-food-actions {
    padding: 0 20px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-now-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
    margin-right: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.order-now-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 16px;
}

.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: #f8f9fa;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.quick-btn.favorite:hover {
    background: #e74c3c;
}

/* No Items Message */
.no-items-message {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
    grid-column: 1 / -1;
}

.no-items-icon {
    font-size: 60px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.no-items-message h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.no-items-message p {
    font-size: 16px;
    margin: 0;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

.explore-menu hr {
    margin: 10px 0px;
    height: 2px;
    background-color: aliceblue;
    border: none;
}

.explore-menu-list .active {
    border: 4px solid tomato;
    padding: 2px;
}

/* Order Notification */
.order-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
    z-index: 1000;
    animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Filter Row */
.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 120px;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-btn.veg.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-color: #27ae60;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.filter-btn.non-veg.active {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-color: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.filter-icon {
    font-size: 16px;
}

.filter-text {
    font-size: 13px;
}

.filter-count {
    font-size: 11px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

.filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.3);
}

/* Search Container */
.search-container {
    flex-shrink: 0;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0 15px;
    min-width: 250px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.search-icon {
    font-size: 16px;
    color: #7f8c8d;
    margin-right: 10px;
}

.search-input {
    border: none;
    outline: none;
    padding: 12px 0;
    font-size: 14px;
    color: #2c3e50;
    background: transparent;
    flex: 1;
    font-weight: 500;
}

.search-input::placeholder {
    color: #bdc3c7;
    font-weight: 400;
}

.clear-search {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 5px;
    font-size: 14px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search:hover {
    background: #e74c3c;
    color: white;
}

/* Media Queries for smaller devices */
@media (max-width: 768px) {
    .explore-menu {
        gap: 20px;
        padding: 15px 0;
    }

    .explore-menu-text {
        max-width: 90%;
        font-size: 15px;
    }

    .explore-menu-list {
        gap: 20px;
        margin: 20px 0;
        justify-content: flex-start;
        padding: 15px 0;
    }

    .explore-menu-list-item {
        flex-shrink: 0;
        min-width: 80px;
    }

    .explore-menu-list img {
        width: 70px;
        height: 70px;
        min-width: 70px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .explore-menu-list img:hover {
        transform: scale(1.05);
    }

    .explore-menu-list p {
        font-size: 14px;
        margin-top: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
    }

    .explore-menu-list .active img {
        border: 3px solid tomato;
        padding: 1px;
    }

    /* Filter Row Mobile */
    .filter-row {
        flex-direction: column;
        gap: 15px;
        margin: 20px 0;
        padding: 15px;
    }

    .filter-buttons {
        gap: 10px;
        justify-content: center;
        width: 100%;
    }

    .filter-btn {
        padding: 10px 15px;
        min-width: 100px;
        font-size: 12px;
    }

    .filter-text {
        font-size: 11px;
    }

    .filter-count {
        font-size: 10px;
    }

    .search-input-wrapper {
        min-width: 200px;
        width: 100%;
    }

    .search-input {
        font-size: 13px;
    }

    .order-notification {
        top: 80px;
        right: 15px;
        left: 15px;
        padding: 12px 20px;
        font-size: 14px;
        text-align: center;
    }

    /* Featured Items Mobile */
    .featured-items-section {
        margin: 30px 0;
        padding: 20px;
    }

    .featured-header h2 {
        font-size: 24px;
    }

    .featured-header p {
        font-size: 14px;
    }

    .toggle-view-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .featured-items-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .featured-food-card {
        border-radius: 15px;
    }

    .featured-food-image {
        height: 180px;
    }

    .featured-food-info {
        padding: 15px;
    }

    .food-name {
        font-size: 18px;
    }

    .food-description {
        font-size: 13px;
    }

    .food-price {
        font-size: 20px;
    }

    .featured-food-actions {
        padding: 0 15px 15px;
    }

    .order-now-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .quick-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .explore-menu {
        gap: 15px;
        padding: 10px 0;
    }

    .explore-menu-text {
        max-width: 95%;
        font-size: 14px;
        text-align: left;
    }

    .explore-menu-list {
        gap: 15px;
        margin: 15px 0;
        padding: 10px 0;
    }

    .explore-menu-list-item {
        min-width: 70px;
    }

    .explore-menu-list img {
        width: 60px;
        height: 60px;
        min-width: 60px;
    }

    .explore-menu-list p {
        font-size: 12px;
        margin-top: 6px;
        max-width: 70px;
    }

    .explore-menu-list .active img {
        border: 2px solid tomato;
        padding: 1px;
    }

    /* Filter Row Small Mobile */
    .filter-row {
        margin: 15px 0;
        padding: 12px;
        border-radius: 12px;
    }

    .filter-buttons {
        gap: 8px;
    }

    .filter-btn {
        padding: 8px 12px;
        min-width: 80px;
        font-size: 11px;
        border-radius: 20px;
    }

    .filter-icon {
        font-size: 14px;
    }

    .filter-text {
        font-size: 10px;
    }

    .filter-count {
        font-size: 9px;
        padding: 1px 4px;
    }

    .search-input-wrapper {
        min-width: 180px;
        padding: 0 12px;
    }

    .search-input {
        padding: 10px 0;
        font-size: 12px;
    }

    .search-icon {
        font-size: 14px;
    }

    .order-notification {
        top: 70px;
        right: 10px;
        left: 10px;
        padding: 10px 15px;
        font-size: 13px;
        border-radius: 20px;
    }

    /* Featured Items Small Mobile */
    .featured-items-section {
        margin: 20px 0;
        padding: 15px;
        border-radius: 15px;
    }

    .featured-header h2 {
        font-size: 20px;
    }

    .featured-header p {
        font-size: 13px;
    }

    .toggle-view-btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .featured-items-grid {
        gap: 15px;
    }

    .featured-food-card {
        border-radius: 12px;
    }

    .featured-food-image {
        height: 160px;
    }

    .food-type-badge {
        top: 10px;
        left: 10px;
        padding: 4px 8px;
        font-size: 10px;
    }

    .food-type-icon {
        font-size: 12px;
    }

    .food-type-text {
        font-size: 9px;
    }

    .category-tag {
        padding: 3px 8px;
        font-size: 10px;
    }

    .featured-food-info {
        padding: 12px;
    }

    .food-name {
        font-size: 16px;
    }

    .food-description {
        font-size: 12px;
    }

    .food-price {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .featured-food-actions {
        padding: 0 12px 12px;
        flex-direction: column;
        gap: 10px;
    }

    .order-now-btn {
        padding: 8px 16px;
        font-size: 12px;
        margin-right: 0;
        width: 100%;
    }

    .quick-actions {
        justify-content: center;
    }

    .quick-btn {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .no-items-message {
        padding: 40px 15px;
    }

    .no-items-icon {
        font-size: 40px;
    }

    .no-items-message h3 {
        font-size: 18px;
    }

    .no-items-message p {
        font-size: 14px;
    }
}

@media (max-width: 360px) {
    .explore-menu {
        gap: 12px;
        padding: 8px 0;
    }

    .explore-menu-list {
        gap: 12px;
        margin: 12px 0;
    }

    .explore-menu-list-item {
        min-width: 60px;
    }

    .explore-menu-list img {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }

    .explore-menu-list p {
        font-size: 11px;
        margin-top: 5px;
        max-width: 60px;
    }
}
