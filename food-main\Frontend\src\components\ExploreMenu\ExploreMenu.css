/* Modern Explore Menu Styles */
.explore-menu {
    display: flex;
    flex-direction: column;
    gap: 60px;
    padding: 40px 0;
    color: #2c3e50;
    font-weight: 500;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    min-height: 100vh;
}

/* Modern Header Section */
.explore-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    padding: 60px 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 30px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.header-content {
    z-index: 2;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-icon {
    font-size: 16px;
}

.header-title {
    font-size: clamp(32px, 5vw, 48px);
    font-weight: 800;
    margin: 0 0 20px 0;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.header-subtitle {
    font-size: clamp(16px, 2vw, 20px);
    line-height: 1.6;
    opacity: 0.9;
    margin: 0 0 40px 0;
    max-width: 90%;
}

.header-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.15);
    padding: 16px 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    font-size: 24px;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-visual {
    position: relative;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-item {
    position: absolute;
    font-size: 40px;
    animation: float 6s ease-in-out infinite;
    opacity: 0.8;
}

.floating-item.item-1 {
    top: 10%;
    left: 20%;
    animation-delay: 0s;
}

.floating-item.item-2 {
    top: 60%;
    left: 10%;
    animation-delay: 1s;
}

.floating-item.item-3 {
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.floating-item.item-4 {
    bottom: 20%;
    right: 25%;
    animation-delay: 3s;
}

.floating-item.item-5 {
    top: 50%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

/* Modern Categories Section */
.categories-section {
    padding: 40px;
    background: white;
    border-radius: 25px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: clamp(28px, 4vw, 36px);
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.section-subtitle {
    font-size: clamp(16px, 2vw, 18px);
    color: #7f8c8d;
    margin: 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.category-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #f1f2f6;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.category-card.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
}

.category-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 15px;
    overflow: hidden;
    flex-shrink: 0;
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-icon {
    font-size: 24px;
    color: white;
}

.category-info {
    flex: 1;
}

.category-name {
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: inherit;
}

.category-description {
    font-size: 14px;
    opacity: 0.7;
    margin: 0 0 12px 0;
}

.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.category-card.active .category-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.badge-dot {
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 50%;
}

.category-arrow {
    font-size: 20px;
    font-weight: 700;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.category-card:hover .category-arrow {
    opacity: 1;
    transform: translateX(5px);
}

/* Featured Items Section */
.featured-items-section {
    margin: 40px 0;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.featured-header {
    text-align: center;
    margin-bottom: 30px;
}

.featured-header h2 {
    font-size: max(2.5vw, 28px);
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.featured-header p {
    color: #7f8c8d;
    font-size: max(1vw, 16px);
    margin: 0 0 20px 0;
}

.toggle-view-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toggle-view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.toggle-icon {
    font-size: 14px;
    transition: transform 0.3s ease;
}

/* Featured Items Grid */
.featured-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.featured-food-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.featured-food-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Food Type Badge */
.food-type-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.95);
    padding: 6px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.food-type-badge.veg {
    border-left: 4px solid #27ae60;
    color: #27ae60;
}

.food-type-badge.non-veg {
    border-left: 4px solid #e74c3c;
    color: #e74c3c;
}

.food-type-icon {
    font-size: 14px;
}

.food-type-text {
    font-size: 10px;
}

/* Food Image */
.featured-food-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.featured-food-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-food-card:hover .featured-food-image img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 20px 15px 15px;
    display: flex;
    justify-content: flex-end;
}

.category-tag {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Food Info */
.featured-food-info {
    padding: 20px;
}

.food-name {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.food-description {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 15px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.food-price {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 15px;
}

/* Food Actions */
.featured-food-actions {
    padding: 0 20px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-now-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
    margin-right: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.order-now-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 16px;
}

.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: #f8f9fa;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.quick-btn.favorite:hover {
    background: #e74c3c;
}

/* No Items Message */
.no-items-message {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
    grid-column: 1 / -1;
}

.no-items-icon {
    font-size: 60px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.no-items-message h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.no-items-message p {
    font-size: 16px;
    margin: 0;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Modern Filter Section */
.modern-filter-section {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-header {
    text-align: center;
    margin-bottom: 30px;
}

.filter-title {
    font-size: clamp(24px, 3vw, 32px);
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.filter-subtitle {
    font-size: clamp(14px, 2vw, 16px);
    color: #7f8c8d;
    margin: 0;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 30px;
    flex-wrap: wrap;
}

.filter-pills {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-pill {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border: 2px solid #e9ecef;
    border-radius: 30px;
    background: white;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 140px;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.filter-pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.filter-pill:hover::before {
    left: 100%;
}

.filter-pill:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filter-pill.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
}

.filter-pill.veg-pill.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-color: #27ae60;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.filter-pill.non-veg-pill.active {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-color: #e74c3c;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.pill-icon {
    font-size: 18px;
}

.pill-text {
    font-size: 13px;
}

.pill-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    margin-left: 5px;
}

.filter-pill.active .pill-count {
    background: rgba(255, 255, 255, 0.3);
}

/* Modern Search */
.modern-search {
    flex-shrink: 0;
    min-width: 350px;
}

.search-box {
    position: relative;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.search-box:focus-within {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.search-icon-container {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

.search-icon {
    font-size: 18px;
    color: #7f8c8d;
}

.modern-search-input {
    width: 100%;
    border: none;
    outline: none;
    padding: 18px 60px 18px 55px;
    font-size: 15px;
    color: #2c3e50;
    background: transparent;
    font-weight: 500;
}

.modern-search-input::placeholder {
    color: #bdc3c7;
    font-weight: 400;
}

.clear-search-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: #e74c3c;
    color: white;
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.clear-search-btn:hover {
    background: #c0392b;
    transform: translateY(-50%) scale(1.1);
}

.search-suggestions {
    position: absolute;
    bottom: -35px;
    left: 20px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-box:focus-within .search-suggestions {
    opacity: 1;
}

.suggestion-tag {
    background: #f8f9fa;
    color: #7f8c8d;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.suggestion-tag:hover {
    background: #667eea;
    color: white;
}

.explore-menu hr {
    margin: 40px 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
    border: none;
}

/* Order Notification */
.order-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
    z-index: 1000;
    animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Filter Row */
.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 120px;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-btn.veg.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-color: #27ae60;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.filter-btn.non-veg.active {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-color: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.filter-icon {
    font-size: 16px;
}

.filter-text {
    font-size: 13px;
}

.filter-count {
    font-size: 11px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

.filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.3);
}

/* Search Container */
.search-container {
    flex-shrink: 0;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0 15px;
    min-width: 250px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.search-icon {
    font-size: 16px;
    color: #7f8c8d;
    margin-right: 10px;
}

.search-input {
    border: none;
    outline: none;
    padding: 12px 0;
    font-size: 14px;
    color: #2c3e50;
    background: transparent;
    flex: 1;
    font-weight: 500;
}

.search-input::placeholder {
    color: #bdc3c7;
    font-weight: 400;
}

.clear-search {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 5px;
    font-size: 14px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search:hover {
    background: #e74c3c;
    color: white;
}

/* Media Queries for smaller devices */
@media (max-width: 768px) {
    .explore-menu {
        gap: 20px;
        padding: 15px 0;
    }

    .explore-menu-text {
        max-width: 90%;
        font-size: 15px;
    }

    .explore-menu-list {
        gap: 20px;
        margin: 20px 0;
        justify-content: flex-start;
        padding: 15px 0;
    }

    .explore-menu-list-item {
        flex-shrink: 0;
        min-width: 80px;
    }

    .explore-menu-list img {
        width: 70px;
        height: 70px;
        min-width: 70px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .explore-menu-list img:hover {
        transform: scale(1.05);
    }

    .explore-menu-list p {
        font-size: 14px;
        margin-top: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
    }

    .explore-menu-list .active img {
        border: 3px solid tomato;
        padding: 1px;
    }

    /* Filter Row Mobile */
    .filter-row {
        flex-direction: column;
        gap: 15px;
        margin: 20px 0;
        padding: 15px;
    }

    .filter-buttons {
        gap: 10px;
        justify-content: center;
        width: 100%;
    }

    .filter-btn {
        padding: 10px 15px;
        min-width: 100px;
        font-size: 12px;
    }

    .filter-text {
        font-size: 11px;
    }

    .filter-count {
        font-size: 10px;
    }

    .search-input-wrapper {
        min-width: 200px;
        width: 100%;
    }

    .search-input {
        font-size: 13px;
    }

    .order-notification {
        top: 80px;
        right: 15px;
        left: 15px;
        padding: 12px 20px;
        font-size: 14px;
        text-align: center;
    }

    /* Featured Items Mobile */
    .featured-items-section {
        margin: 30px 0;
        padding: 20px;
    }

    .featured-header h2 {
        font-size: 24px;
    }

    .featured-header p {
        font-size: 14px;
    }

    .toggle-view-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .featured-items-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .featured-food-card {
        border-radius: 15px;
    }

    .featured-food-image {
        height: 180px;
    }

    .featured-food-info {
        padding: 15px;
    }

    .food-name {
        font-size: 18px;
    }

    .food-description {
        font-size: 13px;
    }

    .food-price {
        font-size: 20px;
    }

    .featured-food-actions {
        padding: 0 15px 15px;
    }

    .order-now-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .quick-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .explore-menu {
        gap: 15px;
        padding: 10px 0;
    }

    .explore-menu-text {
        max-width: 95%;
        font-size: 14px;
        text-align: left;
    }

    .explore-menu-list {
        gap: 15px;
        margin: 15px 0;
        padding: 10px 0;
    }

    .explore-menu-list-item {
        min-width: 70px;
    }

    .explore-menu-list img {
        width: 60px;
        height: 60px;
        min-width: 60px;
    }

    .explore-menu-list p {
        font-size: 12px;
        margin-top: 6px;
        max-width: 70px;
    }

    .explore-menu-list .active img {
        border: 2px solid tomato;
        padding: 1px;
    }

    /* Filter Row Small Mobile */
    .filter-row {
        margin: 15px 0;
        padding: 12px;
        border-radius: 12px;
    }

    .filter-buttons {
        gap: 8px;
    }

    .filter-btn {
        padding: 8px 12px;
        min-width: 80px;
        font-size: 11px;
        border-radius: 20px;
    }

    .filter-icon {
        font-size: 14px;
    }

    .filter-text {
        font-size: 10px;
    }

    .filter-count {
        font-size: 9px;
        padding: 1px 4px;
    }

    .search-input-wrapper {
        min-width: 180px;
        padding: 0 12px;
    }

    .search-input {
        padding: 10px 0;
        font-size: 12px;
    }

    .search-icon {
        font-size: 14px;
    }

    .order-notification {
        top: 70px;
        right: 10px;
        left: 10px;
        padding: 10px 15px;
        font-size: 13px;
        border-radius: 20px;
    }

    /* Featured Items Small Mobile */
    .featured-items-section {
        margin: 20px 0;
        padding: 15px;
        border-radius: 15px;
    }

    .featured-header h2 {
        font-size: 20px;
    }

    .featured-header p {
        font-size: 13px;
    }

    .toggle-view-btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .featured-items-grid {
        gap: 15px;
    }

    .featured-food-card {
        border-radius: 12px;
    }

    .featured-food-image {
        height: 160px;
    }

    .food-type-badge {
        top: 10px;
        left: 10px;
        padding: 4px 8px;
        font-size: 10px;
    }

    .food-type-icon {
        font-size: 12px;
    }

    .food-type-text {
        font-size: 9px;
    }

    .category-tag {
        padding: 3px 8px;
        font-size: 10px;
    }

    .featured-food-info {
        padding: 12px;
    }

    .food-name {
        font-size: 16px;
    }

    .food-description {
        font-size: 12px;
    }

    .food-price {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .featured-food-actions {
        padding: 0 12px 12px;
        flex-direction: column;
        gap: 10px;
    }

    .order-now-btn {
        padding: 8px 16px;
        font-size: 12px;
        margin-right: 0;
        width: 100%;
    }

    .quick-actions {
        justify-content: center;
    }

    .quick-btn {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .no-items-message {
        padding: 40px 15px;
    }

    .no-items-icon {
        font-size: 40px;
    }

    .no-items-message h3 {
        font-size: 18px;
    }

    .no-items-message p {
        font-size: 14px;
    }
}

@media (max-width: 360px) {
    .explore-menu {
        gap: 12px;
        padding: 8px 0;
    }

    .explore-menu-list {
        gap: 12px;
        margin: 12px 0;
    }

    .explore-menu-list-item {
        min-width: 60px;
    }

    .explore-menu-list img {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }

    .explore-menu-list p {
        font-size: 11px;
        margin-top: 5px;
        max-width: 60px;
    }
}
