.explore-menu {
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding: 20px 0;
    color: #262626;
    font-weight: 500;
}

.explore-menu h1 {
    font-size: clamp(24px, 4vw, 32px);
    margin-bottom: 10px;
    color: #333;
}

.explore-menu-text {
    max-width: 70%;
    color: #666;
    line-height: 1.6;
    font-size: clamp(14px, 2vw, 16px);
}

.explore-menu-list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 25px;
    text-align: center;
    margin: 25px 0;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
    padding: 10px 0;
}

.explore-menu-list::-webkit-scrollbar {
    display: none;
}

.explore-menu-list img {
    width: 7.5vw;
    min-width: 80px;
    max-width: 120px;
    height: auto;
    cursor: pointer;
    border-radius: 50%;
    transition: 0.2s;
}

.explore-menu-list p {
    margin-top: 10px;
    color: #747474;
    font-size: max(1.4vw, 16px);
    cursor: pointer;
}

.explore-menu hr {
    margin: 10px 0px;
    height: 2px;
    background-color: aliceblue;
    border: none;
}

.explore-menu-list .active {
    border: 4px solid tomato;
    padding: 2px;
}

/* Media Queries for smaller devices */
@media (max-width: 768px) {
    .explore-menu {
        gap: 20px;
        padding: 15px 0;
    }

    .explore-menu-text {
        max-width: 90%;
        font-size: 15px;
    }

    .explore-menu-list {
        gap: 20px;
        margin: 20px 0;
        justify-content: flex-start;
        padding: 15px 0;
    }

    .explore-menu-list-item {
        flex-shrink: 0;
        min-width: 80px;
    }

    .explore-menu-list img {
        width: 70px;
        height: 70px;
        min-width: 70px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .explore-menu-list img:hover {
        transform: scale(1.05);
    }

    .explore-menu-list p {
        font-size: 14px;
        margin-top: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
    }

    .explore-menu-list .active img {
        border: 3px solid tomato;
        padding: 1px;
    }
}

@media (max-width: 480px) {
    .explore-menu {
        gap: 15px;
        padding: 10px 0;
    }

    .explore-menu-text {
        max-width: 95%;
        font-size: 14px;
        text-align: left;
    }

    .explore-menu-list {
        gap: 15px;
        margin: 15px 0;
        padding: 10px 0;
    }

    .explore-menu-list-item {
        min-width: 70px;
    }

    .explore-menu-list img {
        width: 60px;
        height: 60px;
        min-width: 60px;
    }

    .explore-menu-list p {
        font-size: 12px;
        margin-top: 6px;
        max-width: 70px;
    }

    .explore-menu-list .active img {
        border: 2px solid tomato;
        padding: 1px;
    }
}

@media (max-width: 360px) {
    .explore-menu {
        gap: 12px;
        padding: 8px 0;
    }

    .explore-menu-list {
        gap: 12px;
        margin: 12px 0;
    }

    .explore-menu-list-item {
        min-width: 60px;
    }

    .explore-menu-list img {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }

    .explore-menu-list p {
        font-size: 11px;
        margin-top: 5px;
        max-width: 60px;
    }
}
