*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: Outfit, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  scroll-behavior: smooth;
}

body{
  min-height: 100vh;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a{
  text-decoration: none;
  color: inherit;
}

.app{
  width: 90%;
  max-width: 1200px;
  margin: auto;
  padding: 0 10px;
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .app {
    width: 95%;
    padding: 0 5px;
  }

  body {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .app {
    width: 100%;
    padding: 0 2px;
  }

  body {
    font-size: 13px;
  }
}

/* Utility classes for mobile responsiveness */
.mobile-hidden {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-only {
    display: block;
  }
}

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  button, .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    font-size: 16px;
  }
}